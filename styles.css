/* Enhanced CSS Variables with better organization, performance, and accessibility */
:root {
  /* Color Palette - Google Material Design */
  --primary-blue: #4285F4;
  --primary-green: #34A853;
  --primary-yellow: #FBBC05;
  --primary-red: #EA4335;
  --primary-purple: #9C27B0;
  --primary-orange: #FF9800;

  /* Light Theme Colors */
  --bg-color: #f5f5f5;
  --container-bg: #ffffff;
  --text-color: #333333;
  --text-secondary: #5f6368;
  --text-muted: #9aa0a6;
  --border-color: #e8eaed;
  --status-container-bg: #f8f9fa;
  --bottom-container-bg: #f8f9fa;
  --error-bg: #fce8e6;
  --success-bg: #e6f4ea;
  --warning-bg: #fef7e0;

  /* Component Colors */
  --toggle-bg: rgba(0, 0, 0, 0.2);
  --toggle-handle: white;
  --lang-btn-bg: #ffffff;
  --lang-btn-color: #5f6368;
  --lang-btn-border: #e0e0e0;
  --lang-btn-active-bg: #e8f0fe;
  --lang-btn-active-color: var(--primary-blue);
  --lang-btn-active-border: var(--primary-blue);

  /* Enhanced Gradients with better performance */
  --stat-success-bg: linear-gradient(135deg, #ffffff 0%, #e8f5e9 100%);
  --stat-fail-bg: linear-gradient(135deg, #ffffff 0%, #ffebee 100%);
  --header-gradient: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-green) 25%, var(--primary-yellow) 50%, var(--primary-red) 100%);

  /* Optimized Shadows for better performance */
  --shadow-color: rgba(0, 0, 0, 0.12);
  --shadow-small: 0 1px 3px rgba(0, 0, 0, 0.08);
  --shadow-medium: 0 2px 6px rgba(0, 0, 0, 0.12);
  --shadow-large: 0 4px 12px rgba(0, 0, 0, 0.16);
  --shadow-focus: 0 0 0 2px var(--primary-blue);

  /* Performance-optimized Transitions */
  --transition-fast: 0.15s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 0.35s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-bounce: 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);

  /* Consistent Spacing Scale */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 12px;
  --spacing-lg: 16px;
  --spacing-xl: 20px;
  --spacing-xxl: 24px;

  /* Border Radius Scale */
  --radius-xs: 2px;
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 20px;
  --radius-full: 50%;

  /* Typography Scale */
  --font-size-xs: 11px;
  --font-size-sm: 12px;
  --font-size-base: 14px;
  --font-size-lg: 16px;
  --font-size-xl: 18px;
  --font-size-xxl: 20px;

  /* Z-index Scale */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal: 1040;
  --z-popover: 1050;
  --z-tooltip: 1060;
}

[data-theme="dark"] {
  /* Dark Theme Colors */
  --bg-color: #202124;
  --container-bg: #292a2d;
  --text-color: #e8eaed;
  --text-secondary: #9aa0a6;
  --border-color: #3c4043;
  --status-container-bg: #202124;
  --bottom-container-bg: #202124;

  /* Component Colors - Dark */
  --toggle-bg: rgba(255, 255, 255, 0.2);
  --toggle-handle: #e8eaed;
  --lang-btn-bg: #3c4043;
  --lang-btn-color: #e8eaed;
  --lang-btn-border: #5f6368;
  --lang-btn-active-bg: #174ea6;
  --lang-btn-active-color: #e8eaed;
  --lang-btn-active-border: #8ab4f8;

  /* Gradients - Dark */
  --stat-success-bg: linear-gradient(135deg, #292a2d, #0d3320);
  --stat-fail-bg: linear-gradient(135deg, #292a2d, #3d1c1c);

  /* Shadows - Dark */
  --shadow-color: rgba(0, 0, 0, 0.3);
  --shadow-small: rgba(0, 0, 0, 0.2);
  --shadow-medium: rgba(0, 0, 0, 0.4);
}

/* Enhanced base styles with better performance and accessibility */
*,
*::before,
*::after {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* Optimized font stack with better fallbacks */
:root {
  --font-family-base: 'Product Sans', 'Segoe UI', system-ui, -apple-system, BlinkMacSystemFont, 'Roboto', 'Helvetica Neue', Arial, sans-serif;
  --font-family-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
}

/* Optimize font loading with better performance */
@font-face {
  font-family: 'Product Sans';
  font-display: swap;
  font-weight: 400 700;
}

/* Enhanced body styles with better performance */
body {
  font-family: var(--font-family-base);
  background-color: var(--bg-color);
  color: var(--text-color);
  font-size: var(--font-size-base);
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;

  /* Performance optimizations */
  will-change: background-color, color;
  transition: background-color var(--transition-normal), color var(--transition-normal);

  /* Accessibility improvements */
  overflow-x: hidden;
  scroll-behavior: smooth;
}

/* Respect user's motion preferences */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --shadow-small: 0 2px 4px rgba(0, 0, 0, 0.3);
    --shadow-medium: 0 4px 8px rgba(0, 0, 0, 0.3);
    --shadow-large: 0 6px 16px rgba(0, 0, 0, 0.3);
  }
}

/* Focus management for accessibility */
:focus {
  outline: 2px solid var(--primary-blue);
  outline-offset: 2px;
}

:focus:not(:focus-visible) {
  outline: none;
}

:focus-visible {
  outline: 2px solid var(--primary-blue);
  outline-offset: 2px;
}

/* Enhanced container with better performance and accessibility */
.container {
  width: 320px;
  min-height: 400px;
  padding: 0 0 var(--spacing-xs) 0;
  background-color: var(--container-bg);
  box-shadow: var(--shadow-large);
  border-radius: var(--radius-md);
  overflow: hidden;
  position: relative;

  /* Performance optimizations */
  contain: layout style paint;
  will-change: background-color;
  transition: background-color var(--transition-normal), box-shadow var(--transition-normal);

  /* Enhanced entrance animation */
  animation: container-appear 0.4s var(--transition-normal) both;
}

/* Optimized entrance animation with better performance */
@keyframes container-appear {
  0% {
    opacity: 0;
    transform: translateY(12px) scale(0.96);
    filter: blur(4px);
  }
  50% {
    opacity: 0.8;
    transform: translateY(6px) scale(0.98);
    filter: blur(2px);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
    filter: blur(0);
  }
}

/* Loading state animation */
@keyframes pulse-loading {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
}

/* Error state animation */
@keyframes shake-error {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-2px);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(2px);
  }
}

/* Container states */
.container.loading {
  animation: pulse-loading 1.5s ease-in-out infinite;
}

.container.error {
  animation: shake-error 0.5s ease-in-out;
  border: 2px solid var(--primary-red);
}

.container.success {
  border: 2px solid var(--primary-green);
  transition: border-color var(--transition-normal);
}

/* Enhanced header with better spacing and performance */
.header {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--spacing-lg) var(--spacing-xl);
  position: relative;
  background: var(--container-bg);
  margin-bottom: 0;
  overflow: hidden;
  transition: background-color var(--transition-normal);
}

/* Optimized bottom container */
.bottom-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md) var(--spacing-lg);
  background-color: var(--bottom-container-bg);
  border-top: 1px solid var(--border-color);
  transition: background-color var(--transition-normal), border-color var(--transition-normal);
}

/* Enhanced language selector with better performance */
.lang-selector {
  display: flex;
  gap: var(--spacing-xs);
  justify-content: center;
  z-index: 10;
}

.lang-btn {
  width: 36px;
  height: 36px;
  border-radius: var(--radius-full);
  border: 2px solid var(--lang-btn-border);
  background-color: var(--lang-btn-bg);
  color: var(--lang-btn-color);
  font-weight: 600;
  font-size: 13px;
  cursor: pointer;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  box-shadow: 0 1px 3px var(--shadow-small);
  position: relative;
  overflow: hidden;
  will-change: transform;
}

/* Optimized hover effect */
.lang-btn::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: var(--radius-full);
  transform: translate(-50%, -50%);
  transition: width var(--transition-normal), height var(--transition-normal);
  pointer-events: none;
}

.lang-btn:hover {
  transform: scale(1.05);
  box-shadow: 0 2px 5px var(--shadow-small);
}

.lang-btn:hover::before {
  width: 100%;
  height: 100%;
}

.lang-btn:active {
  transform: scale(0.95);
  transition: transform var(--transition-fast);
}

.lang-btn.lang-active {
  border-color: var(--lang-btn-active-border);
  color: var(--lang-btn-active-color);
  background-color: var(--lang-btn-active-bg);
  box-shadow: 0 2px 5px var(--shadow-small);
  transform: scale(1.05);
}

/* Enhanced dark mode toggle */
.theme-toggle {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: var(--spacing-xs);
  border-radius: var(--radius-sm);
  transition: background-color var(--transition-fast);
}

.theme-toggle:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

[data-theme="dark"] .theme-toggle:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.theme-icon {
  width: 20px;
  height: 20px;
  fill: var(--text-secondary);
  transition: fill var(--transition-normal), transform var(--transition-fast);
}

.theme-toggle:hover .theme-icon {
  transform: scale(1.1);
}

.header::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6px;
  background: repeating-linear-gradient(
    to right,
    #4285F4 0%,
    #4285F4 25%,
    #34A853 25%,
    #34A853 50%,
    #FBBC05 50%,
    #FBBC05 75%,
    #EA4335 75%,
    #EA4335 100%
  );
}

.header::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 6px;
  background: repeating-linear-gradient(
    to right,
    #4285F4 0%,
    #4285F4 25%,
    #34A853 25%,
    #34A853 50%,
    #FBBC05 50%,
    #FBBC05 75%,
    #EA4335 75%,
    #EA4335 100%
  );
}

/* Enhanced typography */
h1 {
  font-size: 20px;
  color: var(--text-color);
  margin-bottom: var(--spacing-md);
  text-align: center;
  font-weight: 500;
  transition: color var(--transition-normal);
  letter-spacing: -0.02em;
}

/* Optimized toggle container */
.toggle-container {
  display: flex;
  align-items: center;
  margin-top: var(--spacing-xs);
  background-color: var(--status-container-bg);
  padding: 6px var(--spacing-md);
  border-radius: var(--radius-xl);
  box-shadow: 0 1px 3px var(--shadow-small);
  transition: background-color var(--transition-normal), box-shadow var(--transition-normal);
}

#toggleStatus {
  margin-left: var(--spacing-md);
  font-size: 14px;
  color: var(--text-color);
  font-weight: 500;
  transition: color var(--transition-normal);
}

/* Enhanced Toggle Switch with better performance and accessibility */
.switch {
  position: relative;
  display: inline-block;
  width: 52px;
  height: 26px;
  flex-shrink: 0;
}

.switch input {
  position: absolute;
  opacity: 0;
  width: 100%;
  height: 100%;
  margin: 0;
  cursor: pointer;
  z-index: 3;
}

.switch input:focus {
  outline: none;
}

.switch input:focus + .slider {
  box-shadow: var(--shadow-focus);
}

.switch input:focus-visible + .slider {
  box-shadow: var(--shadow-focus);
}

.slider {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--toggle-bg);
  border-radius: 26px;
  overflow: hidden;
}

/* Skip link styles for accessibility */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: #000;
  color: #fff;
  padding: 8px;
  text-decoration: none;
  border-radius: 4px;
  z-index: 1000;
}

.skip-link:focus {
  top: 6px;
}

/* Screen reader only content */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Keyboard navigation focus styles */
.keyboard-navigation *:focus {
  outline: 2px solid #4285F4;
  outline-offset: 2px;
}

/* High contrast mode */
.high-contrast {
  filter: contrast(150%);
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
  cursor: pointer;

  /* Performance optimizations */
  contain: layout style paint;
  will-change: background-color;
  transition: background-color var(--transition-normal), box-shadow var(--transition-normal);
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

.slider::before {
  content: "";
  position: absolute;
  height: 18px;
  width: 18px;
  left: 4px;
  top: 4px;
  background-color: var(--toggle-handle);
  border-radius: var(--radius-full);
  z-index: 2;

  /* Enhanced performance */
  contain: layout style paint;
  will-change: transform;
  transition: transform var(--transition-normal), box-shadow var(--transition-normal);
  box-shadow: var(--shadow-small);
}

.slider::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--header-gradient);
  opacity: 0;
  z-index: 1;

  /* Performance optimization */
  will-change: opacity;
  transition: opacity var(--transition-normal);
}

/* Toggle states */
.switch input:checked + .slider {
  background-color: transparent;
}

.switch input:checked + .slider::after {
  opacity: 1;
}

.switch input:checked + .slider::before {
  transform: translateX(26px);
  box-shadow: var(--shadow-medium);
}

/* Hover effects */
.switch:hover .slider::before {
  box-shadow: var(--shadow-medium);
}

.switch input:checked:hover + .slider::before {
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2);
}

/* Active state */
.switch input:active + .slider::before {
  transform: scale(0.95);
}

.switch input:checked:active + .slider::before {
  transform: translateX(26px) scale(0.95);
}

/* Disabled state */
.switch input:disabled + .slider {
  opacity: 0.5;
  cursor: not-allowed;
}

.switch input:disabled + .slider::before {
  cursor: not-allowed;
}

/* Enhanced Status Container with better performance and accessibility */
.status-container {
  display: flex;
  align-items: center;
  margin: var(--spacing-xl);
  padding: var(--spacing-md) var(--spacing-lg);
  background-color: var(--status-container-bg);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-small);
  border-left: 4px solid var(--primary-blue);
  position: relative;
  overflow: hidden;

  /* Performance optimizations */
  contain: layout style paint;
  will-change: border-left-color, background-color;
  transition: all var(--transition-normal);

  /* Accessibility */
  min-height: 44px; /* Touch target size */
}

.status-container::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    transparent 0%,
    transparent 50%,
    rgba(255, 255, 255, 0.03) 50%,
    rgba(255, 255, 255, 0.03) 100%
  );
  z-index: 0;
  pointer-events: none;
}

.status-container > * {
  position: relative;
  z-index: 1;
}

/* Status container states with enhanced animations */
.status-container.loading {
  border-left-color: var(--primary-yellow);
  animation: pulse-status 1.5s ease-in-out infinite;
}

.status-container.active {
  border-left-color: var(--primary-green);
  background-color: var(--success-bg);
}

.status-container.inactive {
  border-left-color: var(--primary-red);
  background-color: var(--error-bg);
}

.status-container.processing {
  border-left-color: var(--primary-blue);
  background-color: var(--status-container-bg);
}

.status-container.error {
  border-left-color: var(--primary-red);
  background-color: var(--error-bg);
  animation: shake-subtle 0.5s ease-in-out;
}

/* Enhanced status animations */
@keyframes pulse-status {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.01);
  }
}

@keyframes shake-subtle {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-2px); }
  75% { transform: translateX(2px); }
}

/* Status icons */
.status-container.active::after {
  content: "✓";
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 16px;
  color: #34A853;
}

.status-container.inactive::after {
  content: "○";
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 16px;
  color: #EA4335;
}

.status-container.processing::after {
  content: "⟳";
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 16px;
  color: #FBBC05;
  animation: spin 2s linear infinite;
}

@keyframes spin {
  0% { transform: translateY(-50%) rotate(0deg); }
  100% { transform: translateY(-50%) rotate(360deg); }
}

/* Bot detection status styling */
.status-container.bot-detected {
  border-left: 4px solid #FF0000;
  background-color: rgba(255, 0, 0, 0.1);
  position: relative;
  padding-right: 40px;
}

.status-container.bot-detected::after {
  content: "⚠️";
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 18px;
  animation: pulse-warning 2s infinite;
}

[data-theme="dark"] .status-container.bot-detected {
  background-color: rgba(255, 0, 0, 0.2);
}

@keyframes pulse-warning {
  0% {
    opacity: 0.7;
    transform: translateY(-50%) scale(1);
  }
  50% {
    opacity: 1;
    transform: translateY(-50%) scale(1.1);
  }
  100% {
    opacity: 0.7;
    transform: translateY(-50%) scale(1);
  }
}

/* Retry button container */
.retry-button-container {
  display: flex;
  justify-content: center;
  margin: 0 0 15px 0;
}

/* Retry button for bot detection */
.retry-button {
  display: none;
  padding: 8px 16px;
  background-color: #4285F4;
  color: white;
  border: none;
  border-radius: 20px;
  font-weight: 500;
  font-size: 14px;
  cursor: pointer;
  text-align: center;
  transition: all 0.3s ease;
  box-shadow: 0 2px 5px var(--shadow-small);
  position: relative;
  overflow: hidden;
  align-items: center;
  justify-content: center;
}

.retry-icon {
  width: 18px;
  height: 18px;
  fill: white;
  margin-right: 6px;
  transition: transform 0.3s ease;
}

.retry-text {
  display: inline-block;
  vertical-align: middle;
}

.retry-button::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.7s ease;
}

.retry-button:hover {
  background-color: #3367d6;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px var(--shadow-small);
}

.retry-button:hover .retry-icon {
  transform: rotate(180deg);
}

.retry-button:hover::before {
  left: 100%;
}

.retry-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px var(--shadow-small);
}

.retry-button.visible {
  display: flex;
  animation: fade-in 0.5s ease;
}



@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

[data-theme="dark"] .retry-button {
  background-color: #4285F4;
  color: white;
}

[data-theme="dark"] .retry-button:hover {
  background-color: #5c9aff;
}

/* Loading state styling */
.status-container.loading {
  border-left: 4px solid #FBBC05;
  animation: pulse 1.5s infinite alternate;
}

@keyframes pulse {
  0% {
    opacity: 0.7;
  }
  100% {
    opacity: 1;
  }
}

.status-label {
  font-weight: 600;
  margin-right: 10px;
  color: #4285F4;
  transition: color 0.3s ease;
}

.status-value {
  color: var(--text-color);
  flex: 1;
  font-weight: 500;
  transition: color 0.3s ease;
}

/* Enhanced Stats Container with better performance and accessibility */
.stats-container {
  display: flex;
  gap: var(--spacing-sm);
  margin: 0 var(--spacing-xl) var(--spacing-xl);

  /* Performance optimization */
  contain: layout;
}

.stat {
  flex: 1;
  padding: var(--spacing-lg) var(--spacing-md);
  border-radius: var(--radius-md);
  text-align: center;
  position: relative;
  overflow: hidden;
  min-height: 80px;
  display: flex;
  flex-direction: column;
  justify-content: center;

  /* Performance optimizations */
  contain: layout style paint;
  will-change: transform, box-shadow;
  transition: all var(--transition-normal);
  box-shadow: var(--shadow-small);

  /* Accessibility */
  cursor: default;
  user-select: none;
}

.stat::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    transparent 0%,
    transparent 50%,
    rgba(255, 255, 255, 0.05) 50%,
    rgba(255, 255, 255, 0.05) 100%
  );
  opacity: 0;
  transition: opacity var(--transition-normal);
  pointer-events: none;
}

/* Hover effects with better performance */
.stat:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-medium);
}

.stat:hover::before {
  opacity: 1;
}

/* Success stat styling */
.stat:first-child {
  background: var(--stat-success-bg);
  border-bottom: 3px solid var(--primary-green);
  color: var(--text-color);
}

.stat:first-child .stat-value {
  color: var(--primary-green);
}

/* Fail stat styling */
.stat:last-child {
  background: var(--stat-fail-bg);
  border-bottom: 3px solid var(--primary-red);
  color: var(--text-color);
}

.stat:last-child .stat-value {
  color: var(--primary-red);
}

/* Active state for stats */
.stat:active {
  transform: translateY(0);
  transition: transform var(--transition-fast);
}

/* Loading state for stats */
.stat.loading {
  animation: pulse-loading 1.5s ease-in-out infinite;
}

.stat.loading .stat-value {
  animation: shimmer 1.5s ease-in-out infinite;
}

@keyframes shimmer {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

.stat-label {
  font-size: 13px;
  color: var(--text-secondary);
  margin-bottom: 8px;
  font-weight: 500;
  transition: color 0.3s ease;
}

.stat-value {
  font-size: 22px;
  font-weight: 600;
  color: var(--text-color);
  transition: color 0.3s ease;
}

#solveCount {
  color: #34A853;
  transition: color 0.3s ease;
}

[data-theme="dark"] #solveCount {
  color: #4AE371; /* Brighter green for dark mode */
}

#failCount {
  color: #EA4335;
  transition: color 0.3s ease;
}

[data-theme="dark"] #failCount {
  color: #FF6B5E; /* Brighter red for dark mode */
}

/* Footer */
.footer {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: var(--text-secondary);
  padding: 12px 20px;
  background-color: var(--bottom-container-bg);
  border-top: 1px solid var(--border-color);
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

.version {
  font-weight: 500;
}

.author {
  font-weight: 500;
  color: var(--text-secondary);
  transition: color 0.3s ease;
}

.author-name {
  font-weight: 700;
  color: #4285F4;
  font-size: 14px;
  letter-spacing: 1px;
  text-transform: uppercase;
  position: relative;
  padding: 0 2px;
  text-shadow: 1px 1px 1px var(--shadow-small);
  transition: all 0.3s ease;
}

[data-theme="dark"] .author-name {
  color: #5C9CFF; /* Brighter blue for dark mode */
}

.author-name:hover {
  color: #EA4335;
  transform: scale(1.05);
}

/* Enhanced Utility Classes for better development experience */

/* Accessibility utilities */
.sr-only {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}

.skip-link {
  position: absolute;
  top: -40px;
  left: var(--spacing-sm);
  background: var(--text-color);
  color: var(--container-bg);
  padding: var(--spacing-sm) var(--spacing-md);
  text-decoration: none;
  border-radius: var(--radius-sm);
  z-index: var(--z-tooltip);
  font-weight: 500;
  transition: top var(--transition-fast);
}

.skip-link:focus {
  top: var(--spacing-sm);
}

/* Focus management */
.keyboard-navigation *:focus {
  outline: 2px solid var(--primary-blue) !important;
  outline-offset: 2px !important;
}

/* High contrast mode */
.high-contrast {
  filter: contrast(150%) brightness(110%);
}

.high-contrast .stat,
.high-contrast .status-container,
.high-contrast .lang-btn {
  border: 1px solid var(--text-color);
}

/* Loading states */
.loading-skeleton {
  background: linear-gradient(
    90deg,
    var(--border-color) 25%,
    var(--status-container-bg) 50%,
    var(--border-color) 75%
  );
  background-size: 200% 100%;
  animation: loading-skeleton 1.5s ease-in-out infinite;
}

@keyframes loading-skeleton {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

/* Error states */
.error-state {
  color: var(--primary-red);
  background-color: var(--error-bg);
  border: 1px solid var(--primary-red);
  border-radius: var(--radius-sm);
  padding: var(--spacing-sm);
}

/* Success states */
.success-state {
  color: var(--primary-green);
  background-color: var(--success-bg);
  border: 1px solid var(--primary-green);
  border-radius: var(--radius-sm);
  padding: var(--spacing-sm);
}

/* Performance optimizations */
.gpu-accelerated {
  transform: translateZ(0);
  will-change: transform;
}

.no-select {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* Responsive design improvements */
@media (max-width: 360px) {
  .container {
    width: 300px;
    margin: 0 auto;
  }

  .stats-container {
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  .stat {
    margin: 0;
  }
}

@media (max-height: 500px) {
  .container {
    min-height: auto;
  }

  .header,
  .status-container,
  .stats-container {
    margin: var(--spacing-md);
  }
}

/* Print styles */
@media print {
  .container {
    box-shadow: none;
    border: 1px solid var(--border-color);
  }

  .theme-toggle,
  .retry-button {
    display: none;
  }
}

/* Dark mode specific optimizations */
[data-theme="dark"] .loading-skeleton {
  background: linear-gradient(
    90deg,
    var(--border-color) 25%,
    rgba(255, 255, 255, 0.1) 50%,
    var(--border-color) 75%
  );
}

/* Performance monitoring (development only) */
.perf-monitor {
  position: fixed;
  top: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: var(--spacing-xs);
  font-size: var(--font-size-xs);
  font-family: var(--font-family-mono);
  z-index: var(--z-tooltip);
  border-radius: 0 0 0 var(--radius-sm);
}
