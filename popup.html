<!DOCTYPE html>
<html lang="id">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="description" content="reCAPTCHA Audio Solver - Automatically solve reCAPTCHA audio challenges">
  <title>reCAPTCHA Audio Solver</title>

  <!-- Preload critical resources -->
  <link rel="preload" href="styles.css" as="style">
  <link rel="preload" href="lang.js" as="script">
  <link rel="preload" href="popup.js" as="script">

  <!-- Stylesheets -->
  <link rel="stylesheet" href="styles.css">
  <link href="https://fonts.googleapis.com/css2?family=Product+Sans:wght@400;500;700&display=swap" rel="stylesheet" media="print" onload="this.media='all'">

  <!-- Accessibility and performance optimizations -->
  <meta name="color-scheme" content="light dark">
  <meta name="theme-color" content="#4285F4">
</head>
<body>
  <!-- Skip to main content for screen readers -->
  <a href="#main-content" class="skip-link">Skip to main content</a>

  <div class="container" role="main" id="main-content">
    <header class="header">
      <h1 id="title">reCAPTCHA Audio Solver</h1>
      <div class="toggle-container" role="group" aria-labelledby="toggle-label">
        <span id="toggle-label" class="sr-only">Solver Status Control</span>
        <label class="switch" for="solverToggle">
          <input
            type="checkbox"
            id="solverToggle"
            role="switch"
            aria-describedby="toggleStatus"
            aria-label="Enable or disable automatic reCAPTCHA solving">
          <span class="slider round" aria-hidden="true"></span>
        </label>
        <span id="toggleStatus" aria-live="polite">Nonaktif</span>
      </div>
    </header>

    <section class="status-container" role="status" aria-live="polite">
      <div id="statusLabel" class="status-label">Status:</div>
      <div id="status" class="status-value" aria-live="polite" aria-atomic="true">Memuat...</div>
    </section>

    <div class="retry-button-container">
      <button
        id="retryButton"
        class="retry-button"
        type="button"
        aria-label="Try again after bot detection"
        aria-describedby="retry-description">
        <svg class="retry-icon" viewBox="0 0 24 24" aria-hidden="true" focusable="false">
          <path d="M17.65,6.35C16.2,4.9 14.21,4 12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20C15.73,20 18.84,17.45 19.73,14H17.65C16.83,16.33 14.61,18 12,18A6,6 0 0,1 6,12A6,6 0 0,1 12,6C13.66,6 15.14,6.69 16.22,7.78L13,11H20V4L17.65,6.35Z" />
        </svg>
        <span class="retry-text" id="retry-description"></span>
      </button>
    </div>

    <section class="stats-container" role="region" aria-labelledby="stats-heading">
      <h2 id="stats-heading" class="sr-only">Statistics</h2>
      <div class="stat" role="group" aria-labelledby="success-label">
        <div id="successLabel" class="stat-label">Berhasil:</div>
        <div
          id="solveCount"
          class="stat-value"
          aria-live="polite"
          aria-atomic="true"
          aria-label="Number of successfully solved captchas">0</div>
      </div>
      <div class="stat" role="group" aria-labelledby="fail-label">
        <div id="failLabel" class="stat-label">Gagal:</div>
        <div
          id="failCount"
          class="stat-value"
          aria-live="polite"
          aria-atomic="true"
          aria-label="Number of failed captcha attempts">0</div>
      </div>
    </section>


    <footer class="footer" role="contentinfo">
      <div class="version" aria-label="Extension version">v3.6.1</div>
      <div class="author">by <span class="author-name">⭐ Moryata ⭐</span></div>
    </footer>

    <div class="bottom-container">
      <div class="lang-selector" role="group" aria-labelledby="lang-heading">
        <span id="lang-heading" class="sr-only">Language Selection</span>
        <button
          id="langEN"
          class="lang-btn"
          type="button"
          aria-label="Switch to English"
          aria-pressed="false">EN</button>
        <button
          id="langID"
          class="lang-btn lang-active"
          type="button"
          aria-label="Switch to Indonesian"
          aria-pressed="true">ID</button>
        <button
          id="langES"
          class="lang-btn"
          type="button"
          aria-label="Switch to Spanish"
          aria-pressed="false">ES</button>
      </div>
      <button
        class="theme-toggle"
        id="themeToggle"
        type="button"
        aria-label="Toggle dark mode"
        aria-pressed="false">
        <svg class="theme-icon" id="themeIcon" viewBox="0 0 24 24" aria-hidden="true" focusable="false">
          <path d="M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2M12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4M12,6A6,6 0 0,1 18,12A6,6 0 0,1 12,18A6,6 0 0,1 6,12A6,6 0 0,1 12,6M12,8A4,4 0 0,0 8,12A4,4 0 0,0 12,16A4,4 0 0,0 16,12A4,4 0 0,0 12,8Z" />
        </svg>
      </button>
    </div>
  </div>

  <!-- Screen reader only content -->
  <div class="sr-only">
    <div id="live-region" aria-live="polite" aria-atomic="true"></div>
    <div id="alert-region" role="alert" aria-atomic="true"></div>
  </div>

  <!-- Scripts with proper loading -->
  <script src="lang.js" defer></script>
  <script src="popup.js" defer></script>
</body>
</html>
