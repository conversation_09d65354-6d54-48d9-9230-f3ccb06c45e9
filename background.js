'use strict';

/**
 * reCAPTCHA Audio Solver v3.6.1 - Enhanced & Optimized
 * Background script with improved performance, security, and reliability
 * <AUTHOR> (Moryata)
 * @version 3.6.1
 */

// Enhanced configuration with better organization and validation
const CONFIG = Object.freeze({
  VERSION: 'v3.6.1',

  // Visual feedback colors
  BADGE_COLORS: Object.freeze({
    ACTIVE: '#4CAF50',
    INACTIVE: '#EA4335',
    PROCESSING: '#FBBC05',
    ERROR: '#FF5722'
  }),

  // Performance tuning
  COOKIE_BATCH_SIZE: 8, // Reduced for better performance
  REQUEST_TIMEOUT: 25000, // Slightly reduced timeout
  MAX_RETRIES: 2, // Reduced retries to prevent server overload

  // Rate limiting
  RATE_LIMIT: {
    MAX_REQUESTS_PER_MINUTE: 30,
    COOLDOWN_PERIOD: 2000 // 2 seconds between requests
  },

  // Security settings
  SECURITY: Object.freeze({
    MAX_RESPONSE_LENGTH: 100,
    MIN_RESPONSE_LENGTH: 1,
    ALLOWED_CONTENT_TYPES: ['text/plain', 'application/json'],
    MAX_AUDIO_URL_LENGTH: 2048
  })
});

// Rate limiting state
const rateLimiter = {
  requests: new Map(), // Track requests per minute
  lastRequest: 0,

  canMakeRequest() {
    const now = Date.now();
    const oneMinuteAgo = now - 60000;

    // Clean old entries
    for (const [timestamp] of this.requests) {
      if (timestamp < oneMinuteAgo) {
        this.requests.delete(timestamp);
      }
    }

    // Check rate limit
    if (this.requests.size >= CONFIG.RATE_LIMIT.MAX_REQUESTS_PER_MINUTE) {
      return false;
    }

    // Check cooldown
    if (now - this.lastRequest < CONFIG.RATE_LIMIT.COOLDOWN_PERIOD) {
      return false;
    }

    return true;
  },

  recordRequest() {
    const now = Date.now();
    this.requests.set(now, true);
    this.lastRequest = now;
  }
};

/**
 * Initialize extension state with enhanced error handling and validation
 * @returns {Promise<void>}
 */
async function initializeExtension() {
  const defaultSettings = Object.freeze({
    enabled: true,
    solveCount: 0,
    failCount: 0,
    lastStatus: "Aktif",
    botDetected: false,
    lastUpdated: Date.now(),
    version: CONFIG.VERSION,
    installDate: Date.now()
  });

  try {
    // Validate existing settings
    const existingData = await chrome.storage.local.get(Object.keys(defaultSettings));
    const validatedSettings = { ...defaultSettings };

    // Preserve valid existing data
    Object.keys(existingData).forEach(key => {
      if (existingData[key] !== undefined && existingData[key] !== null) {
        validatedSettings[key] = existingData[key];
      }
    });

    // Update version if needed
    validatedSettings.version = CONFIG.VERSION;
    validatedSettings.lastUpdated = Date.now();

    await chrome.storage.local.set(validatedSettings);
    await chrome.action.setBadgeBackgroundColor({ color: CONFIG.BADGE_COLORS.ACTIVE });

    console.log(`reCAPTCHA Audio Solver ${CONFIG.VERSION} initialized successfully`);

  } catch (error) {
    console.error('Failed to initialize extension:', error);

    // Fallback initialization without async/await
    chrome.storage.local.set(defaultSettings, () => {
      if (chrome.runtime.lastError) {
        console.error('Fallback initialization failed:', chrome.runtime.lastError);
      }
    });

    chrome.action.setBadgeBackgroundColor({ color: CONFIG.BADGE_COLORS.ACTIVE });
  }
}

// Initialize extension on install/startup
chrome.runtime.onInstalled.addListener(initializeExtension);
chrome.runtime.onStartup.addListener(initializeExtension);

/**
 * Enhanced cookie deletion with better performance, security, and error handling
 * @returns {Promise<{success: boolean, count?: number, error?: string}>}
 */
async function deleteCookiesForRecaptcha() {
  const logPrefix = '[reCAPTCHA Audio Solver]';
  const startTime = performance.now();

  try {
    // Define domains with priority order (most important first)
    const domains = Object.freeze([
      '.recaptcha.net',
      'www.recaptcha.net',
      'recaptcha.net',
      '.google.com',
      'www.google.com'
    ]);

    // Enhanced cookie retrieval with better error handling and timeout
    const cookiePromises = domains.map(domain => {
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error(`Timeout for ${domain}`)), 3000)
      );

      return Promise.race([
        chrome.cookies.getAll({ domain }),
        timeoutPromise
      ]).catch(error => {
        console.debug(`${logPrefix} Failed to get cookies for ${domain}:`, error.message);
        return [];
      });
    });

    const cookieArrays = await Promise.allSettled(cookiePromises);
    const allCookies = cookieArrays
      .filter(result => result.status === 'fulfilled')
      .flatMap(result => result.value);

    // Enhanced cookie filtering with security validation
    const filteredCookies = allCookies.filter(cookie => {
      // Validate cookie object
      if (!cookie || !cookie.domain || !cookie.name) return false;

      const domain = cookie.domain.toLowerCase();
      const name = cookie.name.toLowerCase();
      const path = (cookie.path || '').toLowerCase();

      // reCAPTCHA specific cookies
      if (domain.includes('recaptcha')) return true;

      // Google cookies - only reCAPTCHA related
      if (domain.includes('google')) {
        return name.includes('captcha') ||
               path.includes('recaptcha') ||
               name.includes('nid') ||
               name.includes('1p_jar') ||
               name.includes('__recaptcha');
      }

      return false;
    });

    // Remove duplicates using Set for better performance
    const uniqueCookiesMap = new Map();
    filteredCookies.forEach(cookie => {
      const key = `${cookie.domain}|${cookie.name}|${cookie.path}`;
      if (!uniqueCookiesMap.has(key)) {
        uniqueCookiesMap.set(key, cookie);
      }
    });

    const uniqueCookies = Array.from(uniqueCookiesMap.values());

    if (uniqueCookies.length === 0) {
      console.log(`${logPrefix} No cookies to delete`);
      return { success: true, count: 0 };
    }

    // Optimized batch deletion with better error handling
    const batchSize = Math.min(CONFIG.COOKIE_BATCH_SIZE, uniqueCookies.length);
    let deletedCount = 0;
    const errors = [];

    for (let i = 0; i < uniqueCookies.length; i += batchSize) {
      const batch = uniqueCookies.slice(i, i + batchSize);

      const deletePromises = batch.map(async (cookie) => {
        try {
          // Validate URL construction
          const domain = cookie.domain.startsWith('.') ? cookie.domain.slice(1) : cookie.domain;
          const url = `https://${domain}${cookie.path || '/'}`;

          // Validate URL length for security
          if (url.length > 2048) {
            throw new Error('URL too long');
          }

          const result = await chrome.cookies.remove({
            url,
            name: cookie.name,
            storeId: cookie.storeId
          });

          if (result) {
            deletedCount++;
            return true;
          }
          return false;

        } catch (error) {
          errors.push(`${cookie.name}: ${error.message}`);
          return false;
        }
      });

      await Promise.allSettled(deletePromises);

      // Small delay between batches to prevent overwhelming the browser
      if (i + batchSize < uniqueCookies.length) {
        await new Promise(resolve => setTimeout(resolve, 50));
      }
    }

    const duration = performance.now() - startTime;
    console.log(`${logPrefix} Deleted ${deletedCount}/${uniqueCookies.length} cookies in ${duration.toFixed(2)}ms`);

    if (errors.length > 0 && errors.length < 5) {
      console.debug(`${logPrefix} Some deletion errors:`, errors.slice(0, 3));
    }

    return {
      success: true,
      count: deletedCount,
      duration: Math.round(duration)
    };

  } catch (error) {
    const duration = performance.now() - startTime;
    console.error(`${logPrefix} Cookie deletion failed after ${duration.toFixed(2)}ms:`, error);
    return {
      success: false,
      error: error.message,
      duration: Math.round(duration)
    };
  }
}

/**
 * Enhanced audio processing with better performance, security, and reliability
 * @param {string} audioUrl - The audio URL to process
 * @param {string} serverUrl - The server URL to send the request to
 * @returns {Promise<{success: boolean, text?: string, error?: string, status?: number}>}
 */
async function processAudioFromServer(audioUrl, serverUrl) {
  const logPrefix = '[reCAPTCHA Audio Solver]';
  const startTime = performance.now();

  // Input validation
  if (!audioUrl || !serverUrl) {
    return { success: false, error: 'Missing required parameters' };
  }

  // URL length validation for security
  if (audioUrl.length > CONFIG.SECURITY.MAX_AUDIO_URL_LENGTH) {
    return { success: false, error: 'Audio URL too long' };
  }

  // Rate limiting check
  if (!rateLimiter.canMakeRequest()) {
    return {
      success: false,
      error: 'Rate limit exceeded',
      errorType: 'RateLimit'
    };
  }

  try {
    const serverHostname = new URL(serverUrl).hostname;
    console.log(`${logPrefix} Processing audio with server: ${serverHostname}`);

    // Record request for rate limiting
    rateLimiter.recordRequest();

    // Enhanced user agents with more recent versions and better variety
    const userAgents = Object.freeze([
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
      'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:122.0) Gecko/20100101 Firefox/122.0'
    ]);

    // Secure random user agent selection
    const userAgent = userAgents[Math.floor(Math.random() * userAgents.length)];

    // Enhanced security headers
    const headers = Object.freeze({
      'Content-Type': 'application/x-www-form-urlencoded',
      'Accept': 'text/plain, application/json',
      'Accept-Language': 'en-US,en;q=0.9',
      'Accept-Encoding': 'gzip, deflate, br',
      'User-Agent': userAgent,
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      'Pragma': 'no-cache',
      'DNT': '1',
      'Sec-Fetch-Dest': 'empty',
      'Sec-Fetch-Mode': 'cors',
      'Sec-Fetch-Site': 'cross-site',
      'X-Requested-With': 'XMLHttpRequest'
    });

    // Enhanced timeout handling with AbortController
    const controller = new AbortController();
    const timeoutId = setTimeout(() => {
      controller.abort();
      console.warn(`${logPrefix} Request timeout after ${CONFIG.REQUEST_TIMEOUT}ms`);
    }, CONFIG.REQUEST_TIMEOUT);

    let response;
    try {
      // Prepare request body with proper encoding
      const requestBody = new URLSearchParams({
        input: audioUrl,
        lang: 'en'
      }).toString();

      // Make the request with enhanced security and error handling
      response = await fetch(serverUrl, {
        method: 'POST',
        headers,
        body: requestBody,
        credentials: 'omit',
        mode: 'cors',
        cache: 'no-store',
        redirect: 'error', // Don't follow redirects for security
        signal: controller.signal,
        referrerPolicy: 'no-referrer'
      });

    } finally {
      clearTimeout(timeoutId);
    }

    // Enhanced response validation
    if (!response.ok) {
      const errorMsg = `Server error: ${response.status} ${response.statusText}`;
      console.error(`${logPrefix} ${errorMsg}`);
      return {
        success: false,
        error: errorMsg,
        status: response.status,
        duration: Math.round(performance.now() - startTime)
      };
    }

    // Validate content type for security
    const contentType = response.headers.get('content-type') || '';
    const isValidContentType = CONFIG.SECURITY.ALLOWED_CONTENT_TYPES.some(type =>
      contentType.toLowerCase().includes(type)
    );

    if (!isValidContentType) {
      console.error(`${logPrefix} Invalid content type: ${contentType}`);
      return {
        success: false,
        error: 'Invalid response content type'
      };
    }

    // Get response text with size limit
    const text = await response.text();
    const trimmedText = text.trim();

    // Enhanced response validation
    const validationResult = validateAudioResponse(trimmedText);
    if (!validationResult.isValid) {
      console.error(`${logPrefix} Response validation failed: ${validationResult.reason}`);
      return {
        success: false,
        error: validationResult.reason,
        duration: Math.round(performance.now() - startTime)
      };
    }

    // Success
    const duration = performance.now() - startTime;
    console.log(`${logPrefix} Successfully processed audio: "${trimmedText}" (${trimmedText.length} chars, ${duration.toFixed(2)}ms)`);

    return {
      success: true,
      text: trimmedText,
      duration: Math.round(duration)
    };

  } catch (error) {
    const duration = performance.now() - startTime;

    if (error.name === 'AbortError') {
      console.error(`${logPrefix} Request timeout after ${duration.toFixed(2)}ms`);
      return {
        success: false,
        error: 'Request timeout',
        errorType: 'Timeout',
        duration: Math.round(duration)
      };
    }

    console.error(`${logPrefix} Error processing audio after ${duration.toFixed(2)}ms:`, error.message);
    return {
      success: false,
      error: error.message || 'Unknown error',
      errorType: error.name || 'Error',
      duration: Math.round(duration)
    };
  }
}

/**
 * Validate audio response with enhanced security checks
 * @param {string} text - The response text to validate
 * @returns {{isValid: boolean, reason?: string}}
 */
function validateAudioResponse(text) {
  if (!text || typeof text !== 'string') {
    return { isValid: false, reason: 'Empty or invalid response' };
  }

  // Length validation
  if (text.length < CONFIG.SECURITY.MIN_RESPONSE_LENGTH ||
      text.length > CONFIG.SECURITY.MAX_RESPONSE_LENGTH) {
    return { isValid: false, reason: `Response length invalid: ${text.length}` };
  }

  // Security patterns check
  const dangerousPatterns = [
    /<script/i, /<iframe/i, /<object/i, /<embed/i,
    /javascript:/i, /data:/i, /vbscript:/i,
    /<|>/, /[<>]/, /script/i, /eval\(/i, /function\(/i
  ];

  if (dangerousPatterns.some(pattern => pattern.test(text))) {
    return { isValid: false, reason: 'Invalid response pattern detected' };
  }

  // Known invalid responses
  const invalidResponses = new Set([
    '0', '1', '2', '3', '4', '5', '6', '7', '8', '9',
    'error', 'fail', 'failed', 'null', 'undefined', 'none'
  ]);

  if (invalidResponses.has(text.toLowerCase())) {
    return { isValid: false, reason: 'Known invalid response' };
  }

  // For short responses, ensure they contain only letters
  if (text.length < 3 && !/^[a-zA-Z]+$/.test(text)) {
    return { isValid: false, reason: 'Invalid short response format' };
  }

  return { isValid: true };
}

/**
 * Enhanced message listener with better error handling, validation, and performance
 */
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  const logPrefix = '[reCAPTCHA Audio Solver]';
  const startTime = performance.now();

  // Enhanced request validation
  if (!isValidRequest(request)) {
    safelySendResponse(sendResponse, {
      success: false,
      error: 'Invalid request format',
      duration: Math.round(performance.now() - startTime)
    });
    return false;
  }

  // Enhanced sender validation
  if (!isValidSender(sender)) {
    console.warn(`${logPrefix} Message from invalid sender context`);
    safelySendResponse(sendResponse, {
      success: false,
      error: 'Invalid sender context',
      duration: Math.round(performance.now() - startTime)
    });
    return false;
  }

  try {
    // Route to appropriate handler
    switch (request.action) {
      case "updateStatus":
        handleStatusUpdate(request, startTime);
        return false; // No response needed

      case "deleteCookies":
        handleDeleteCookies(sendResponse, startTime);
        return true; // Async response

      case "processAudio":
        handleProcessAudio(request, sendResponse, startTime);
        return true; // Async response

      case "getStats":
        handleGetStats(sendResponse, startTime);
        return true; // Async response

      default:
        console.warn(`${logPrefix} Unknown action: ${request.action}`);
        safelySendResponse(sendResponse, {
          success: false,
          error: 'Unknown action',
          duration: Math.round(performance.now() - startTime)
        });
        return false;
    }
  } catch (error) {
    console.error(`${logPrefix} Error handling message:`, error);
    safelySendResponse(sendResponse, {
      success: false,
      error: error.message,
      duration: Math.round(performance.now() - startTime)
    });
    return false;
  }
});

/**
 * Validate incoming request structure
 * @param {any} request - The request to validate
 * @returns {boolean} - Whether the request is valid
 */
function isValidRequest(request) {
  return request &&
         typeof request === 'object' &&
         typeof request.action === 'string' &&
         request.action.length > 0 &&
         request.action.length < 50;
}

/**
 * Validate message sender
 * @param {chrome.runtime.MessageSender} sender - The message sender
 * @returns {boolean} - Whether the sender is valid
 */
function isValidSender(sender) {
  return sender &&
         (sender.tab || sender.url) &&
         sender.id === chrome.runtime.id;
}

/**
 * Handle status updates with enhanced badge management and validation
 * @param {Object} request - The status update request
 * @param {number} startTime - Request start time for performance tracking
 */
async function handleStatusUpdate(request, startTime = performance.now()) {
  const logPrefix = '[reCAPTCHA Audio Solver]';

  try {
    // Validate request structure
    if (!request.stats || typeof request.stats !== 'object') {
      console.warn(`${logPrefix} Invalid stats in status update`);
      return;
    }

    const stats = request.stats;
    const status = request.status || 'unknown';

    // Validate and sanitize stats
    const solvedCount = Math.max(0, Math.min(9999, parseInt(stats.solved) || 0));
    const failedCount = Math.max(0, Math.min(9999, parseInt(stats.failed) || 0));
    const isEnabled = Boolean(stats.enabled);

    // Update badge text with validation
    const badgeText = solvedCount > 0 ?
      (solvedCount > 999 ? '999+' : solvedCount.toString()) : '';

    await chrome.action.setBadgeText({ text: badgeText });

    // Determine badge color based on state
    let badgeColor = CONFIG.BADGE_COLORS.INACTIVE;
    if (isEnabled) {
      switch (status) {
        case 'processing':
          badgeColor = CONFIG.BADGE_COLORS.PROCESSING;
          break;
        case 'error':
          badgeColor = CONFIG.BADGE_COLORS.ERROR;
          break;
        default:
          badgeColor = CONFIG.BADGE_COLORS.ACTIVE;
      }
    }

    await chrome.action.setBadgeBackgroundColor({ color: badgeColor });

    // Update storage with validated data
    const updateData = {
      solveCount: solvedCount,
      failCount: failedCount,
      lastStatus: status.substring(0, 100), // Limit status length
      lastUpdated: Date.now(),
      enabled: isEnabled
    };

    await chrome.storage.local.set(updateData);

    const duration = performance.now() - startTime;
    console.debug(`${logPrefix} Status updated in ${duration.toFixed(2)}ms`);

  } catch (error) {
    console.error(`${logPrefix} Error updating status:`, error);

    // Fallback badge update
    try {
      await chrome.action.setBadgeBackgroundColor({ color: CONFIG.BADGE_COLORS.ERROR });
    } catch (fallbackError) {
      console.error(`${logPrefix} Fallback badge update failed:`, fallbackError);
    }
  }
}

/**
 * Enhanced helper function to safely send response with better error handling and validation
 * @param {Function} sendResponseFunc - The sendResponse function
 * @param {Object} response - The response object to send
 * @returns {boolean} - Whether the response was sent successfully
 */
const safelySendResponse = (sendResponseFunc, response) => {
  const logPrefix = '[reCAPTCHA Audio Solver]';

  try {
    // Check for runtime errors first
    if (chrome.runtime.lastError) {
      const errorMsg = chrome.runtime.lastError.message;

      // Define ignorable errors to reduce noise
      const ignorableErrors = new Set([
        "The message port closed before a response was received.",
        "A listener indicated an asynchronous response by returning true, but the message channel closed before a response was received",
        "Extension context invalidated.",
        "Could not establish connection. Receiving end does not exist."
      ]);

      const isIgnorable = ignorableErrors.has(errorMsg) ||
                         errorMsg.includes("receiving end does not exist") ||
                         errorMsg.includes("port closed");

      if (!isIgnorable) {
        console.warn(`${logPrefix} Runtime error: ${errorMsg}`);
      }
      return false;
    }

    // Validate sendResponse function
    if (typeof sendResponseFunc !== 'function') {
      console.error(`${logPrefix} sendResponse is not a function`);
      return false;
    }

    // Validate response object
    if (!response || typeof response !== 'object') {
      console.error(`${logPrefix} Invalid response object`);
      return false;
    }

    // Add timestamp to response for debugging
    const enhancedResponse = {
      ...response,
      timestamp: Date.now()
    };

    try {
      sendResponseFunc(enhancedResponse);
      return true;

    } catch (error) {
      // Handle specific error cases
      if (error.message) {
        const errorMsg = error.message.toLowerCase();

        if (errorMsg.includes('port closed') ||
            errorMsg.includes('extension context invalidated') ||
            errorMsg.includes('receiving end does not exist')) {
          console.debug(`${logPrefix} Message port closed, ignoring response`);
          return false;
        }
      }

      // Re-throw unexpected errors
      throw error;
    }

  } catch (error) {
    console.error(`${logPrefix} Error sending response:`, error.message);
    return false;
  }
};

/**
 * Handle delete cookies request with enhanced error handling and performance tracking
 * @param {Function} sendResponse - The response function
 * @param {number} startTime - Request start time
 */
async function handleDeleteCookies(sendResponse, startTime = performance.now()) {
  const logPrefix = '[reCAPTCHA Audio Solver]';

  try {
    const result = await deleteCookiesForRecaptcha();

    // Add performance metrics
    result.totalDuration = Math.round(performance.now() - startTime);

    safelySendResponse(sendResponse, result);

  } catch (error) {
    console.error(`${logPrefix} Error in handleDeleteCookies:`, error);

    safelySendResponse(sendResponse, {
      success: false,
      error: error.message || 'Unknown error',
      duration: Math.round(performance.now() - startTime)
    });
  }
}

/**
 * Handle audio processing request with enhanced validation and retry logic
 * @param {Object} request - The audio processing request
 * @param {Function} sendResponse - The response function
 * @param {number} startTime - Request start time
 */
async function handleProcessAudio(request, sendResponse, startTime = performance.now()) {
  const logPrefix = '[reCAPTCHA Audio Solver]';

  // Enhanced parameter validation
  const validationResult = validateAudioRequest(request);
  if (!validationResult.isValid) {
    safelySendResponse(sendResponse, {
      success: false,
      error: validationResult.error,
      duration: Math.round(performance.now() - startTime)
    });
    return;
  }

  // Rate limiting check
  if (!rateLimiter.canMakeRequest()) {
    safelySendResponse(sendResponse, {
      success: false,
      error: 'Rate limit exceeded. Please wait before retrying.',
      errorType: 'RateLimit',
      duration: Math.round(performance.now() - startTime)
    });
    return;
  }

  // Process the audio with enhanced retry logic
  let lastError = null;
  let lastResult = null;

  for (let attempt = 1; attempt <= CONFIG.MAX_RETRIES; attempt++) {
    try {
      console.log(`${logPrefix} Audio processing attempt ${attempt}/${CONFIG.MAX_RETRIES}`);

      const result = await processAudioFromServer(request.audioUrl, request.serverUrl);
      lastResult = result;

      if (result.success) {
        // Add total processing time
        result.totalDuration = Math.round(performance.now() - startTime);
        result.attempts = attempt;

        safelySendResponse(sendResponse, result);
        return;
      }

      lastError = result.error;

      // Don't retry on certain errors
      if (shouldNotRetry(result)) {
        console.log(`${logPrefix} Not retrying due to error type: ${result.error}`);
        break;
      }

      // Exponential backoff with jitter
      if (attempt < CONFIG.MAX_RETRIES) {
        const baseDelay = Math.pow(2, attempt) * 1000;
        const jitter = Math.random() * 500; // Add up to 500ms jitter
        const delay = baseDelay + jitter;

        console.log(`${logPrefix} Waiting ${delay.toFixed(0)}ms before retry ${attempt + 1}`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }

    } catch (error) {
      lastError = error.message;
      console.error(`${logPrefix} Attempt ${attempt} failed:`, error.message);

      if (attempt < CONFIG.MAX_RETRIES) {
        const delay = Math.pow(2, attempt) * 1000;
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }

  // All attempts failed
  const finalResponse = {
    success: false,
    error: lastError || 'All retry attempts failed',
    attempts: CONFIG.MAX_RETRIES,
    totalDuration: Math.round(performance.now() - startTime)
  };

  // Include last result details if available
  if (lastResult) {
    finalResponse.lastStatus = lastResult.status;
    finalResponse.lastErrorType = lastResult.errorType;
  }

  safelySendResponse(sendResponse, finalResponse);
}

/**
 * Validate audio processing request
 * @param {Object} request - The request to validate
 * @returns {{isValid: boolean, error?: string}}
 */
function validateAudioRequest(request) {
  if (!request.audioUrl || !request.serverUrl) {
    return {
      isValid: false,
      error: 'Missing required parameters: audioUrl and serverUrl'
    };
  }

  // Validate URL formats
  try {
    const audioUrl = new URL(request.audioUrl);
    const serverUrl = new URL(request.serverUrl);

    // Additional security checks
    if (audioUrl.protocol !== 'https:' && audioUrl.protocol !== 'http:') {
      return { isValid: false, error: 'Invalid audio URL protocol' };
    }

    if (serverUrl.protocol !== 'https:') {
      return { isValid: false, error: 'Server URL must use HTTPS' };
    }

  } catch (error) {
    return { isValid: false, error: 'Invalid URL format' };
  }

  return { isValid: true };
}

/**
 * Determine if an error should not be retried
 * @param {Object} result - The result object from audio processing
 * @returns {boolean} - Whether to skip retrying
 */
function shouldNotRetry(result) {
  // Don't retry on client errors or rate limiting
  if (result.status >= 400 && result.status < 500) {
    return true;
  }

  // Don't retry on rate limiting
  if (result.errorType === 'RateLimit') {
    return true;
  }

  // Don't retry on validation errors
  if (result.error && result.error.includes('validation')) {
    return true;
  }

  return false;
}

/**
 * Handle get stats request
 * @param {Function} sendResponse - The response function
 * @param {number} startTime - Request start time
 */
async function handleGetStats(sendResponse, startTime = performance.now()) {
  try {
    const data = await chrome.storage.local.get([
      'solveCount', 'failCount', 'enabled', 'lastStatus', 'botDetected', 'lastUpdated'
    ]);

    const stats = {
      success: true,
      solveCount: data.solveCount || 0,
      failCount: data.failCount || 0,
      enabled: data.enabled !== undefined ? data.enabled : true,
      lastStatus: data.lastStatus || 'Unknown',
      botDetected: data.botDetected || false,
      lastUpdated: data.lastUpdated || Date.now(),
      duration: Math.round(performance.now() - startTime)
    };

    safelySendResponse(sendResponse, stats);

  } catch (error) {
    console.error('[reCAPTCHA Audio Solver] Error getting stats:', error);

    safelySendResponse(sendResponse, {
      success: false,
      error: error.message || 'Failed to get stats',
      duration: Math.round(performance.now() - startTime)
    });
  }
}
