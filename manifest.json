{"manifest_version": 3, "name": "reCAPTCHA Audio Solver", "version": "3.6.1", "description": "Automatically solve reCAPTCHA audio challenges with enhanced performance, multi-language support, and improved reliability. Features optimized processing and better error handling.", "author": "<PERSON> (Moryata)", "homepage_url": "https://github.com/moryata/Recaptcha-Audio", "icons": {"16": "icons/icon16.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}, "action": {"default_popup": "popup.html", "default_title": "reCAPTCHA Audio Solver", "default_icon": {"16": "icons/icon16.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}}, "permissions": ["storage", "activeTab", "cookies", "scripting"], "optional_permissions": ["background"], "host_permissions": ["*://*.google.com/recaptcha/*", "*://*.recaptcha.net/recaptcha/*", "*://*.gstatic.com/recaptcha/*", "https://engageub.pythonanywhere.com/*", "https://engageub1.pythonanywhere.com/*", "https://engageub2.pythonanywhere.com/*"], "background": {"service_worker": "background.js", "type": "module"}, "content_scripts": [{"matches": ["*://*/recaptcha/*", "*://*.google.com/recaptcha/*", "*://*.recaptcha.net/recaptcha/*", "*://*.gstatic.com/recaptcha/*"], "js": ["content.js"], "run_at": "document_idle", "all_frames": true, "match_about_blank": false, "match_origin_as_fallback": false, "world": "ISOLATED"}], "web_accessible_resources": [{"resources": ["icons/*"], "matches": ["*://*.google.com/*", "*://*.recaptcha.net/*"]}], "content_security_policy": {"extension_pages": "script-src 'self'; object-src 'self'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; connect-src 'self' https://engageub.pythonanywhere.com https://engageub1.pythonanywhere.com https://engageub2.pythonanywhere.com https://www.google.com https://www.recaptcha.net https://www.gstatic.com; img-src 'self' data: https://www.gstatic.com; worker-src 'self'; frame-src 'none'; base-uri 'self'; form-action 'none'"}, "minimum_chrome_version": "88", "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA1234567890abcdef", "externally_connectable": {"matches": ["*://*.google.com/recaptcha/*", "*://*.recaptcha.net/recaptcha/*"]}, "incognito": "split", "offline_enabled": false}