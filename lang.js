'use strict';

/**
 * Enhanced Language System for reCAPTCHA Audio Solver v3.6.1
 * Optimized with lazy loading, caching, and better fallback mechanisms
 * <AUTHOR> (Moryata)
 * @version 3.6.1
 */

// Enhanced translation system with better organization and performance
const LanguageSystem = (() => {
  // Private variables
  let currentLanguage = 'id'; // Default to Indonesian
  let loadedLanguages = new Set();
  let translationCache = new Map();
  let fallbackChain = ['en', 'id']; // Fallback order

  // Core translations - always loaded for performance
  const coreTranslations = Object.freeze({
    en: {
      // Essential UI elements
      title: "reCAPTCHA Audio Solver",
      loading: "Loading...",
      error: "Error",
      active: "Active",
      inactive: "Inactive",

      // Critical status messages
      toggleActive: "Active",
      toggleInactive: "Inactive",
      statusLabel: "Status:",
      processing: "Processing...",
      botDetected: "Bot detected! Try again later",
      tryAgain: "Try Again",

      // Basic labels
      successLabel: "Success:",
      failLabel: "Failed:",
      by: "by",

      // Time formatting
      justNow: "Just now",
      secondsAgo: "seconds ago",
      minutesAgo: "minutes ago",
      noData: "No data"
    },
    id: {
      // Essential UI elements
      title: "reCAPTCHA Audio Solver",
      loading: "Memuat...",
      error: "Error",
      active: "Aktif",
      inactive: "Nonaktif",

      // Critical status messages
      toggleActive: "Aktif",
      toggleInactive: "Nonaktif",
      statusLabel: "Status:",
      processing: "Memproses...",
      botDetected: "Bot terdeteksi! Coba lagi nanti",
      tryAgain: "Coba Lagi",

      // Basic labels
      successLabel: "Berhasil:",
      failLabel: "Gagal:",
      by: "by",

      // Time formatting
      justNow: "Baru saja",
      secondsAgo: "detik yang lalu",
      minutesAgo: "menit yang lalu",
      noData: "Tidak ada data"
    },

    es: {
      // Essential UI elements
      title: "reCAPTCHA Audio Solver",
      loading: "Cargando...",
      error: "Error",
      active: "Activo",
      inactive: "Inactivo",

      // Critical status messages
      toggleActive: "Activo",
      toggleInactive: "Inactivo",
      statusLabel: "Estado:",
      processing: "Procesando...",
      botDetected: "¡Bot detectado! Inténtalo más tarde",
      tryAgain: "Intentar de nuevo",

      // Basic labels
      successLabel: "Éxito:",
      failLabel: "Fallido:",
      by: "por",

      // Time formatting
      justNow: "Ahora mismo",
      secondsAgo: "segundos atrás",
      minutesAgo: "minutos atrás",
      noData: "Sin datos"
    }
  });

  // Extended translations - loaded on demand
  const extendedTranslations = Object.freeze({
    en: {
      // Extended status messages
      success: "Success",
      successAlready: "Success (already solved)",
      waiting: "Waiting for captcha",
      fallbackToImage: "Max attempts reached, switching to image",

      // Settings and advanced features
      settings: "Settings",
      darkMode: "Dark Mode",
      language: "Language",
      server: "Server",

      // Advanced messages
      retryAfterDelay: "Retrying in {0} seconds...",
      serverError: "Server error: {0}",
      networkError: "Network connection failed",
      extensionError: "Extension error occurred",

      // Accessibility
      solverToggleLabel: "Enable or disable automatic reCAPTCHA solving",
      languageSelection: "Language Selection",
      themeToggle: "Toggle dark mode",
      statisticsHeading: "Statistics"
    },
    id: {
      // Extended status messages
      success: "Berhasil",
      successAlready: "Berhasil (sudah terselesaikan)",
      waiting: "Menunggu captcha",
      fallbackToImage: "Batas percobaan tercapai, beralih ke gambar",

      // Settings and advanced features
      settings: "Pengaturan",
      darkMode: "Mode Gelap",
      language: "Bahasa",
      server: "Server",

      // Advanced messages
      retryAfterDelay: "Mencoba lagi dalam {0} detik...",
      serverError: "Error server: {0}",
      networkError: "Koneksi jaringan gagal",
      extensionError: "Terjadi error pada ekstensi",

      // Accessibility
      solverToggleLabel: "Aktifkan atau nonaktifkan penyelesaian reCAPTCHA otomatis",
      languageSelection: "Pemilihan Bahasa",
      themeToggle: "Beralih mode gelap",
      statisticsHeading: "Statistik"
    },

    es: {
      // Extended status messages
      success: "Éxito",
      successAlready: "Éxito (ya resuelto)",
      waiting: "Esperando captcha",
      fallbackToImage: "Límite de intentos alcanzado, cambiando a imagen",

      // Settings and advanced features
      settings: "Configuración",
      darkMode: "Modo Oscuro",
      language: "Idioma",
      server: "Servidor",

      // Advanced messages
      retryAfterDelay: "Reintentando en {0} segundos...",
      serverError: "Error del servidor: {0}",
      networkError: "Falló la conexión de red",
      extensionError: "Ocurrió un error en la extensión",

      // Accessibility
      solverToggleLabel: "Habilitar o deshabilitar la resolución automática de reCAPTCHA",
      languageSelection: "Selección de Idioma",
      themeToggle: "Alternar modo oscuro",
      statisticsHeading: "Estadísticas"
    }
  });

  /**
   * Enhanced translation function with caching and interpolation
   * @param {string} key - Translation key
   * @param {Array|string} params - Parameters for string interpolation
   * @returns {string} - Translated text
   */
  function getTranslation(key, params = []) {
    if (!key || typeof key !== 'string') {
      console.warn('Invalid translation key:', key);
      return key || '';
    }

    // Create cache key
    const cacheKey = `${currentLanguage}:${key}`;

    // Check cache first
    if (translationCache.has(cacheKey)) {
      const cached = translationCache.get(cacheKey);
      return interpolateString(cached, params);
    }

    // Try to get translation with fallback chain
    let translation = null;
    const searchLanguages = [currentLanguage, ...fallbackChain];

    for (const lang of searchLanguages) {
      // Check core translations first
      if (coreTranslations[lang]?.[key]) {
        translation = coreTranslations[lang][key];
        break;
      }

      // Check extended translations if available
      if (extendedTranslations[lang]?.[key]) {
        translation = extendedTranslations[lang][key];
        break;
      }
    }

    // Fallback to key if no translation found
    if (!translation) {
      console.warn(`Translation not found for key: ${key}`);
      translation = key;
    }

    // Cache the result
    translationCache.set(cacheKey, translation);

    // Clean cache if it gets too large
    if (translationCache.size > 100) {
      const firstKey = translationCache.keys().next().value;
      translationCache.delete(firstKey);
    }

    return interpolateString(translation, params);
  }

  /**
   * String interpolation for parameterized translations
   * @param {string} str - String with placeholders
   * @param {Array|string} params - Parameters to interpolate
   * @returns {string} - Interpolated string
   */
  function interpolateString(str, params) {
    if (!params || (Array.isArray(params) && params.length === 0)) {
      return str;
    }

    const paramArray = Array.isArray(params) ? params : [params];

    return str.replace(/\{(\d+)\}/g, (match, index) => {
      const paramIndex = parseInt(index, 10);
      return paramArray[paramIndex] !== undefined ? paramArray[paramIndex] : match;
    });
  }

  /**
   * Set current language with validation and caching
   * @param {string} lang - Language code
   * @returns {boolean} - Success status
   */
  function setLanguage(lang) {
    if (!lang || typeof lang !== 'string') {
      console.warn('Invalid language code:', lang);
      return false;
    }

    // Validate language exists in core translations
    if (!coreTranslations[lang]) {
      console.warn('Unsupported language:', lang);
      return false;
    }

    // Update current language
    const previousLanguage = currentLanguage;
    currentLanguage = lang;

    // Clear translation cache when language changes
    if (previousLanguage !== lang) {
      translationCache.clear();
    }

    // Mark language as loaded
    loadedLanguages.add(lang);

    // Save to localStorage with error handling
    try {
      localStorage.setItem('reCAPTCHA_solver_lang', lang);
    } catch (error) {
      console.debug('Failed to save language preference:', error.message);
    }

    // Dispatch language change event
    try {
      document.dispatchEvent(new CustomEvent('languageChanged', {
        detail: { language: lang, previousLanguage }
      }));
    } catch (error) {
      console.debug('Failed to dispatch language change event:', error.message);
    }

    return true;
  }

  /**
   * Get current language
   * @returns {string} - Current language code
   */
  function getCurrentLanguage() {
    return currentLanguage;
  }

  /**
   * Get available languages
   * @returns {Array} - Array of available language codes
   */
  function getAvailableLanguages() {
    return Object.keys(coreTranslations);
  }

  /**
   * Check if language is supported
   * @param {string} lang - Language code to check
   * @returns {boolean} - Whether language is supported
   */
  function isLanguageSupported(lang) {
    return coreTranslations.hasOwnProperty(lang);
  }

  /**
   * Initialize language system
   */
  function initializeLanguageSystem() {
    try {
      // Try to load from localStorage
      const storedLang = localStorage.getItem('reCAPTCHA_solver_lang');

      if (storedLang && isLanguageSupported(storedLang)) {
        currentLanguage = storedLang;
      } else {
        // Detect browser language
        const browserLang = navigator.language?.split('-')[0] || 'en';
        if (isLanguageSupported(browserLang)) {
          currentLanguage = browserLang;
        }
      }

      loadedLanguages.add(currentLanguage);

    } catch (error) {
      console.debug('Failed to initialize language system:', error.message);
      currentLanguage = 'id'; // Safe fallback
    }
  }

  // Public API
  return Object.freeze({
    t: getTranslation,
    setLanguage,
    getLanguage: getCurrentLanguage,
    getAvailableLanguages,
    isLanguageSupported,
    init: initializeLanguageSystem
  });
})();

// Initialize the language system
LanguageSystem.init();

// Export functions for backward compatibility
const t = LanguageSystem.t;
const setLanguage = LanguageSystem.setLanguage;
const getLanguage = LanguageSystem.getLanguage;
