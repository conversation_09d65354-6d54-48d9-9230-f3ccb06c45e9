'use strict';

/**
 * reCAPTCHA Audio Solver v3.6.1 - Optimized
 * Enhanced version with improved performance and reliability
 */

(function () {
  'use strict';

  try {
    // Check if extension context is valid at startup
    if (!chrome || !chrome.runtime) {
      console.warn('[reCAPTCHA Audio Solver] Chrome runtime not available');
      return;
    }

    try {
      // Test extension context validity
      const testId = chrome.runtime.id;
      if (!testId) {
        console.warn('[reCAPTCHA Audio Solver] Extension context invalid at startup');
        return;
      }
    } catch (error) {
      console.warn('[reCAPTCHA Audio Solver] Extension context error at startup:', error.message);
      return;
    }

  // Enhanced Configuration with better organization and performance tuning
  const CONFIG = Object.freeze({
    VERSION: 'v3.6.1',

    // Performance settings
    PERFORMANCE: Object.freeze({
      MAX_ATTEMPTS_PER_SERVER: 2, // Reduced for better performance
      MAX_TOTAL_ATTEMPTS: 4, // Reduced to prevent server overload
      POLLING_INTERVAL: 800, // Increased for better performance
      RETRY_DELAY: 1500, // Increased delay between retries
      AUDIO_TIMEOUT: 25000, // Reduced timeout
      DOM_CACHE_TTL: 3000, // DOM cache time-to-live
      THROTTLE_DELAY: 200, // Throttle DOM operations
      MAX_PROCESSING_TIME: 60000 // Maximum time for one captcha
    }),

    // DOM selectors with priority order
    SELECTORS: Object.freeze({
      CHECKBOX: '.recaptcha-checkbox-border',
      AUDIO_BUTTON: '#recaptcha-audio-button',
      IMAGE_BUTTON: '#recaptcha-image-button',
      IMAGE_SELECT: '#rc-imageselect',
      AUDIO_SOURCE: '#audio-source',
      RESPONSE_FIELD: '.rc-audiochallenge-response-field',
      AUDIO_RESPONSE: '#audio-response',
      AUDIO_ERROR_MESSAGE: '.rc-audiochallenge-error-message',
      RELOAD_BUTTON: '#recaptcha-reload-button',
      DOSCAPTCHA: '.rc-doscaptcha-body',
      VERIFY_BUTTON: '#recaptcha-verify-button',
      CAPTCHA_FRAME: 'iframe[src*="recaptcha"]'
    }),

    // Server configuration with fallback
    SERVERS: Object.freeze([
      'https://engageub.pythonanywhere.com',
      'https://engageub1.pythonanywhere.com',
      'https://engageub2.pythonanywhere.com'
    ]),

    // Enhanced validation patterns
    VALIDATION: Object.freeze({
      INVALID_PATTERNS: [
        /<script/i, /<iframe/i, /<object/i, /<embed/i,
        /javascript:/i, /data:/i, /vbscript:/i,
        /<|>/, /script/i, /eval\(/i
      ],
      MIN_RESPONSE_LENGTH: 1,
      MAX_RESPONSE_LENGTH: 100,
      VALID_RESPONSE_REGEX: /^[a-zA-Z0-9\s\-.,!?]+$/,
      INVALID_SINGLE_CHARS: new Set(['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'])
    }),

    // Security settings
    SECURITY: Object.freeze({
      MAX_URL_LENGTH: 2048,
      ALLOWED_DOMAINS: new Set(['google.com', 'recaptcha.net', 'gstatic.com']),
      MAX_RETRIES_PER_SESSION: 10
    })
  });

  // Enhanced state management with better encapsulation and validation
  const state = Object.seal({
    // Processing state
    solved: false,
    waiting: false,
    processing: false,

    // Server management
    serverIdx: 0,
    serverTry: 0,
    totalAttempts: 0,
    sessionRetries: 0,

    // Audio processing
    audioUrl: "",
    lastAudioUrl: "",
    audioProcessStartTime: 0,

    // Extension state
    enabled: true,
    solveCount: 0,
    failCount: 0,

    // Operation flags
    processingCookies: false,
    lastProcessTime: 0,
    lastDomUpdate: 0,

    // Resource management
    intervalId: null,
    timeoutId: null,
    cleanupTimeoutId: null,

    // Context management
    extensionValid: true,
    shutdownRequested: false,
    initializationComplete: false,

    // Performance tracking
    startTime: Date.now(),
    lastSuccessTime: 0,
    averageProcessingTime: 0
  });

  // Enhanced DOM cache with better performance and memory management
  class DOMCache {
    constructor(ttl = CONFIG.PERFORMANCE.DOM_CACHE_TTL) {
      this.cache = new Map();
      this.ttl = ttl;
      this.lastCleanup = Date.now();
    }

    get(selector) {
      const now = Date.now();

      // Periodic cleanup
      if (now - this.lastCleanup > this.ttl) {
        this.cleanup();
      }

      const cached = this.cache.get(selector);
      if (cached && now - cached.timestamp < this.ttl) {
        // Verify element is still in DOM
        if (cached.element && cached.element.isConnected) {
          return cached.element;
        } else {
          this.cache.delete(selector);
        }
      }

      // Query new element
      try {
        const element = document.querySelector(selector);
        if (element) {
          this.cache.set(selector, {
            element,
            timestamp: now
          });
        }
        return element;
      } catch (error) {
        console.warn('[reCAPTCHA Audio Solver] DOM query error:', error.message);
        return null;
      }
    }

    cleanup() {
      const now = Date.now();
      for (const [selector, cached] of this.cache) {
        if (now - cached.timestamp > this.ttl || !cached.element.isConnected) {
          this.cache.delete(selector);
        }
      }
      this.lastCleanup = now;
    }

    clear() {
      this.cache.clear();
      this.lastCleanup = Date.now();
    }

    size() {
      return this.cache.size;
    }
  }

  const domCache = new DOMCache();

  // Enhanced helper functions with better performance and error handling
  const log = (msg, level = 'info', showConsole = true) => {
    if (!msg || state.shutdownRequested) return;

    const timestamp = new Date().toISOString();
    const logMsg = `[reCAPTCHA Audio Solver ${CONFIG.VERSION}] ${msg}`;

    if (showConsole) {
      switch (level) {
        case 'error':
          console.error(logMsg);
          break;
        case 'warn':
          console.warn(logMsg);
          break;
        case 'debug':
          console.debug(logMsg);
          break;
        default:
          console.log(logMsg);
      }
    }

    // Update status with throttling
    updateStatus(msg);
  };

  // Optimized DOM selector with enhanced caching and error handling
  const $ = (selector) => {
    try {
      // Early returns for invalid states
      if (!selector ||
          !state.extensionValid ||
          state.shutdownRequested ||
          typeof selector !== 'string') {
        return null;
      }

      // Use enhanced DOM cache
      return domCache.get(selector);

    } catch (error) {
      // Handle extension context errors
      if (handleExtensionContextError(error, 'DOM selector')) {
        return null;
      }

      log(`DOM selector error for "${selector}": ${error.message}`, 'warn', false);
      return null;
    }
  };

  // Enhanced element visibility check with performance optimization
  const isHidden = (element) => {
    if (!element) return true;

    try {
      // Fast checks first
      if (!element.offsetParent && element.offsetWidth === 0 && element.offsetHeight === 0) {
        return true;
      }

      // Check computed style only if needed
      const style = getComputedStyle(element);
      return style.display === 'none' ||
             style.visibility === 'hidden' ||
             style.opacity === '0';

    } catch (error) {
      log(`Visibility check error: ${error.message}`, 'warn', false);
      return true;
    }
  };

  // Enhanced extension context validation with better error handling
  const isExtensionValid = () => {
    try {
      // Multiple validation checks for robustness
      if (!chrome || !chrome.runtime) return false;

      // Test runtime ID access - most reliable indicator
      const id = chrome.runtime.id;
      if (!id) return false;

      // Test if we can access extension URL
      const url = chrome.runtime.getURL('');
      if (!url) return false;

      return true;

    } catch (error) {
      // Any error indicates invalid context
      return false;
    }
  };

  // Enhanced performance monitoring
  const performanceMonitor = {
    startTime: Date.now(),
    operations: new Map(),

    start(operation) {
      this.operations.set(operation, performance.now());
    },

    end(operation) {
      const startTime = this.operations.get(operation);
      if (startTime) {
        const duration = performance.now() - startTime;
        this.operations.delete(operation);
        return duration;
      }
      return 0;
    },

    getUptime() {
      return Date.now() - this.startTime;
    },

    reset() {
      this.operations.clear();
      this.startTime = Date.now();
    }
  };

  // Global error handler for extension context invalidation
  const handleExtensionContextError = (error, source = 'unknown') => {
    if (error && error.message &&
        (error.message.includes('Extension context invalidated') ||
         error.message.includes('message port closed') ||
         error.message.includes('receiving end does not exist'))) {
      console.warn(`[reCAPTCHA Audio Solver] Extension context invalidated in ${source}`);
      state.extensionValid = false;
      state.shutdownRequested = true;
      cleanup();
      return true;
    }
    return false;
  };

  // Safe wrapper for chrome.runtime.sendMessage with context validation
  const safeSendMessage = (message, callback) => {
    if (!isExtensionValid()) {
      console.warn('[reCAPTCHA Audio Solver] Extension context invalidated, stopping operations');
      state.extensionValid = false;
      state.shutdownRequested = true;
      cleanup();
      return false;
    }

    try {
      chrome.runtime.sendMessage(message, (response) => {
        if (chrome.runtime.lastError) {
          const error = chrome.runtime.lastError.message;
          if (error.includes('Extension context invalidated') ||
              error.includes('message port closed') ||
              error.includes('receiving end does not exist')) {
            console.warn('[reCAPTCHA Audio Solver] Extension context invalidated during message');
            state.extensionValid = false;
            state.shutdownRequested = true;
            cleanup();
            return;
          }
        }
        if (callback && typeof callback === 'function') {
          callback(response);
        }
      });
      return true;
    } catch (error) {
      console.warn('[reCAPTCHA Audio Solver] Error sending message:', error.message);
      state.extensionValid = false;
      state.shutdownRequested = true;
      cleanup();
      return false;
    }
  };

  // Enhanced response validation with comprehensive security checks
  const isValidResponse = (text) => {
    if (!text || typeof text !== 'string') {
      return { valid: false, reason: 'Empty or invalid text' };
    }

    // Length validation
    if (text.length < CONFIG.VALIDATION.MIN_RESPONSE_LENGTH ||
        text.length > CONFIG.VALIDATION.MAX_RESPONSE_LENGTH) {
      return { valid: false, reason: `Invalid length: ${text.length}` };
    }

    // Security pattern validation
    if (CONFIG.VALIDATION.INVALID_PATTERNS.some(pattern => pattern.test(text))) {
      return { valid: false, reason: 'Contains invalid patterns' };
    }

    // Known invalid single character responses
    if (text.length === 1 && CONFIG.VALIDATION.INVALID_SINGLE_CHARS.has(text)) {
      return { valid: false, reason: 'Invalid single character' };
    }

    // Format validation for short responses
    if (text.length < 3 && !/^[a-zA-Z]+$/.test(text)) {
      return { valid: false, reason: 'Invalid short response format' };
    }

    // General format validation
    if (!CONFIG.VALIDATION.VALID_RESPONSE_REGEX.test(text)) {
      return { valid: false, reason: 'Invalid character format' };
    }

    return { valid: true };
  };

  // Enhanced debounce with better memory management
  const debounce = (func, wait, immediate = false) => {
    let timeout;
    let lastArgs;

    const debounced = function executedFunction(...args) {
      lastArgs = args;

      const later = () => {
        timeout = null;
        if (!immediate) {
          func.apply(this, lastArgs);
        }
      };

      const callNow = immediate && !timeout;
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);

      if (callNow) {
        func.apply(this, args);
      }
    };

    debounced.cancel = () => {
      clearTimeout(timeout);
      timeout = null;
    };

    return debounced;
  };

  // Throttle function for performance optimization
  const throttle = (func, limit) => {
    let inThrottle;
    return function(...args) {
      if (!inThrottle) {
        func.apply(this, args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    };
  };

  // Enhanced status update with throttling and better error handling
  const updateStatus = throttle((status) => {
    if (!status || !state.extensionValid || state.shutdownRequested) return;

    const now = Date.now();
    const data = {
      solveCount: state.solveCount,
      failCount: state.failCount,
      lastStatus: status.substring(0, 100), // Limit status length
      lastUpdated: now,
      sessionUptime: now - state.startTime
    };

    // Enhanced storage update with better error handling
    const updateStorage = async () => {
      try {
        if (!chrome?.storage?.local) {
          throw new Error('Storage not available');
        }

        await chrome.storage.local.set(data);

        // Send status update only if storage was successful
        if (state.extensionValid && !state.shutdownRequested) {
          safeSendMessage({
            action: 'updateStatus',
            status,
            stats: {
              solved: state.solveCount,
              failed: state.failCount,
              enabled: state.enabled,
              processing: state.waiting || state.processing,
              uptime: data.sessionUptime
            }
          });
        }

      } catch (error) {
        // Handle storage errors gracefully
        if (error.message?.includes('Extension context invalidated')) {
          state.extensionValid = false;
          state.shutdownRequested = true;
          cleanup();
        } else {
          log(`Storage update failed: ${error.message}`, 'warn', false);
        }
      }
    };

    // Use async/await with fallback
    if (typeof chrome.storage.local.set === 'function') {
      updateStorage();
    } else {
      // Fallback for older Chrome versions
      try {
        chrome.storage.local.set(data, () => {
          if (chrome.runtime.lastError) {
            log(`Storage fallback error: ${chrome.runtime.lastError.message}`, 'warn', false);
          } else if (state.extensionValid && !state.shutdownRequested) {
            safeSendMessage({
              action: 'updateStatus',
              status,
              stats: {
                solved: state.solveCount,
                failed: state.failCount,
                enabled: state.enabled
              }
            });
          }
        });
      } catch (error) {
        state.extensionValid = false;
        state.shutdownRequested = true;
        cleanup();
      }
    }
  }, CONFIG.PERFORMANCE.THROTTLE_DELAY);

  // Enhanced audio processing with better performance, validation, and error handling
  function getTextFromAudio(url) {
    // Enhanced pre-conditions check
    if (state.waiting ||
        state.solved ||
        state.processing ||
        !state.extensionValid ||
        state.shutdownRequested ||
        state.sessionRetries >= CONFIG.SECURITY.MAX_RETRIES_PER_SESSION) {
      return;
    }

    // Validate URL
    if (!url || url.length > CONFIG.SECURITY.MAX_URL_LENGTH) {
      log('Invalid audio URL', 'error');
      return retryAudio();
    }

    // Start performance monitoring
    performanceMonitor.start('audioProcessing');

    // Update state
    state.waiting = true;
    state.processing = true;
    state.serverTry++;
    state.totalAttempts++;
    state.sessionRetries++;
    state.lastProcessTime = Date.now();
    state.audioProcessStartTime = Date.now();

    const serverUrl = CONFIG.SERVERS[state.serverIdx];
    const processedUrl = url.replace("recaptcha.net", "google.com");

    log(`Kirim audio ke server ${state.serverIdx + 1}/${CONFIG.SERVERS.length} (Percobaan ${state.serverTry}/${CONFIG.PERFORMANCE.MAX_ATTEMPTS_PER_SERVER}, Total ${state.totalAttempts}/${CONFIG.PERFORMANCE.MAX_TOTAL_ATTEMPTS})`);

    // Enhanced timeout handling
    const timeoutId = setTimeout(() => {
      if (state.waiting) {
        const duration = performanceMonitor.end('audioProcessing');
        state.waiting = false;
        state.processing = false;
        log(`Audio processing timeout after ${duration.toFixed(2)}ms`, 'warn');
        retryAudio();
      }
    }, CONFIG.PERFORMANCE.AUDIO_TIMEOUT);

    // Enhanced message sending with better error handling
    const messageSent = safeSendMessage({
      action: 'processAudio',
      audioUrl: processedUrl,
      serverUrl,
      attempt: state.serverTry,
      totalAttempts: state.totalAttempts
    }, response => {
      clearTimeout(timeoutId);
      const processingDuration = performanceMonitor.end('audioProcessing');

      state.waiting = false;
      state.processing = false;

      // Early returns for error conditions
      if (state.solved || !state.extensionValid || state.shutdownRequested) {
        return;
      }

      // Enhanced response validation
      if (!response) {
        log('No response received', 'warn');
        return retryAudio();
      }

      if (!response.success) {
        log(`Server error: ${response.error || 'Unknown error'}`, 'warn');
        return retryAudio();
      }

      if (!response.text) {
        log('Empty response text', 'warn');
        return retryAudio();
      }

      const text = response.text.trim();
      const validation = isValidResponse(text);

      if (!validation.valid) {
        log(`Response validation failed: ${validation.reason} - "${text}"`, 'warn');
        return retryAudio();
      }

      // Enhanced DOM element verification
      performanceMonitor.start('domVerification');

      // Clear cache for fresh queries
      domCache.clear();

      const src = $(CONFIG.SELECTORS.AUDIO_SOURCE);
      const resp = $(CONFIG.SELECTORS.AUDIO_RESPONSE);
      const verify = $(CONFIG.SELECTORS.VERIFY_BUTTON);

      const domDuration = performanceMonitor.end('domVerification');

      // Comprehensive element validation
      if (!src || !resp || !verify) {
        log('Required DOM elements not found', 'warn');
        return retryAudio();
      }

      if (src.src !== state.audioUrl) {
        log('Audio source mismatch', 'warn');
        return retryAudio();
      }

      if (resp.value) {
        log('Response field already filled', 'warn');
        return retryAudio();
      }

      if (isHidden(verify)) {
        log('Verify button is hidden', 'warn');
        return retryAudio();
      }

      // Fill response and submit
      try {
        resp.value = text;
        resp.dispatchEvent(new Event('input', { bubbles: true }));
        resp.dispatchEvent(new Event('change', { bubbles: true }));

        // Enhanced verification with delay
        setTimeout(() => {
          if (resp.value === text && !state.solved) {
            verify.click();

            // Update stats and state
            state.solveCount++;
            state.solved = true;
            state.lastSuccessTime = Date.now();

            // Update average processing time
            const totalTime = Date.now() - state.audioProcessStartTime;
            state.averageProcessingTime = state.averageProcessingTime === 0 ?
              totalTime : (state.averageProcessingTime + totalTime) / 2;

            log(`Audio berhasil diproses: "${text}" (${processingDuration.toFixed(2)}ms processing, ${domDuration.toFixed(2)}ms DOM)`);

            // Update storage asynchronously
            chrome.storage.local.set({
              solveCount: state.solveCount,
              lastSuccessTime: state.lastSuccessTime,
              averageProcessingTime: Math.round(state.averageProcessingTime)
            }).catch(() => {
              // Silently handle storage errors
            });
          } else {
            log('Response verification failed', 'warn');
            retryAudio();
          }
        }, 150); // Increased delay for better reliability

      } catch (error) {
        log(`Error filling response: ${error.message}`, 'error');
        retryAudio();
      }
    });

    // Enhanced fallback handling
    if (!messageSent) {
      clearTimeout(timeoutId);
      performanceMonitor.end('audioProcessing');
      state.waiting = false;
      state.processing = false;

      if (state.extensionValid && !state.shutdownRequested) {
        log('Message sending failed, retrying', 'warn');
        retryAudio();
      }
    }
  }

  // Enhanced retry logic with intelligent backoff and better decision making
  function retryAudio() {
    // Reset processing states
    state.waiting = false;
    state.processing = false;

    // Early return if already solved or shutting down
    if (state.solved || state.shutdownRequested || !state.extensionValid) {
      return;
    }

    // Check session limits
    if (state.sessionRetries >= CONFIG.SECURITY.MAX_RETRIES_PER_SESSION) {
      fallbackToImage("Session retry limit reached");
      return;
    }

    // Check total attempts limit
    if (state.totalAttempts >= CONFIG.PERFORMANCE.MAX_TOTAL_ATTEMPTS) {
      fallbackToImage(`Max attempts reached (${CONFIG.PERFORMANCE.MAX_TOTAL_ATTEMPTS}), switching to image`);
      return;
    }

    // Check processing time limit
    const processingTime = Date.now() - state.audioProcessStartTime;
    if (processingTime > CONFIG.PERFORMANCE.MAX_PROCESSING_TIME) {
      fallbackToImage("Processing time limit exceeded");
      return;
    }

    // Determine retry strategy
    if (state.serverTry < CONFIG.PERFORMANCE.MAX_ATTEMPTS_PER_SERVER) {
      // Retry with same server
      log(`Retry dengan server ${state.serverIdx + 1} (attempt ${state.serverTry + 1}/${CONFIG.PERFORMANCE.MAX_ATTEMPTS_PER_SERVER})`, 'debug');

      // Intelligent delay calculation
      const baseDelay = CONFIG.PERFORMANCE.RETRY_DELAY;
      const backoffMultiplier = Math.pow(1.5, state.serverTry);
      const jitter = Math.random() * 500; // Add jitter to prevent thundering herd
      const delay = Math.min(baseDelay * backoffMultiplier + jitter, 10000); // Cap at 10 seconds

      setTimeout(() => {
        if (!state.solved && !state.shutdownRequested && state.extensionValid) {
          getTextFromAudio(state.audioUrl);
        }
      }, delay);

    } else {
      // Move to next server
      state.serverTry = 0;
      state.serverIdx = (state.serverIdx + 1) % CONFIG.SERVERS.length;

      if (state.serverIdx === 0) {
        // All servers attempted, fallback to image
        fallbackToImage("Semua server gagal, beralih ke gambar");
      } else {
        log(`Beralih ke server ${state.serverIdx + 1}/${CONFIG.SERVERS.length}`, 'info');

        // Longer delay when switching servers
        const serverSwitchDelay = CONFIG.PERFORMANCE.RETRY_DELAY * 2;
        setTimeout(() => {
          if (!state.solved && !state.shutdownRequested && state.extensionValid) {
            getTextFromAudio(state.audioUrl);
          }
        }, serverSwitchDelay);
      }
    }
  }

  // Enhanced fallback function with better error handling and analytics
  function fallbackToImage(message, reason = 'unknown') {
    performanceMonitor.start('fallbackToImage');

    try {
      const imgBtn = $(CONFIG.SELECTORS.IMAGE_BUTTON);

      if (imgBtn && !isHidden(imgBtn)) {
        imgBtn.click();
        log(`Fallback to image: ${message}`, 'info');
      } else {
        log(`Fallback failed - image button not available: ${message}`, 'warn');
      }

      // Update state
      state.solved = true;
      state.failCount++;
      state.processing = false;
      state.waiting = false;

      // Enhanced storage update with analytics
      const fallbackData = {
        failCount: state.failCount,
        lastFailReason: reason,
        lastFailTime: Date.now(),
        sessionRetries: state.sessionRetries,
        averageProcessingTime: state.averageProcessingTime
      };

      chrome.storage.local.set(fallbackData).catch(error => {
        log(`Storage update failed during fallback: ${error.message}`, 'warn', false);
      });

      // Reset counters for next attempt
      resetCounters();

      const duration = performanceMonitor.end('fallbackToImage');
      log(`Fallback completed in ${duration.toFixed(2)}ms`, 'debug', false);

    } catch (error) {
      log(`Error during fallback: ${error.message}`, 'error');
      performanceMonitor.end('fallbackToImage');
    }
  }

  // Enhanced counter reset with state validation
  function resetCounters() {
    state.totalAttempts = 0;
    state.serverTry = 0;
    state.serverIdx = 0;
    state.audioUrl = "";
    state.lastAudioUrl = "";
    state.audioProcessStartTime = 0;

    // Clear any pending timeouts
    if (state.timeoutId) {
      clearTimeout(state.timeoutId);
      state.timeoutId = null;
    }

    // Clear DOM cache to ensure fresh queries for next captcha
    domCache.clear();
  }

  // Enhanced initialization with better error handling
  function initializeSolver() {
    // Check extension context validity before initialization
    if (!isExtensionValid()) {
      console.warn('[reCAPTCHA Audio Solver] Extension context invalid during initialization');
      state.extensionValid = false;
      state.shutdownRequested = true;
      return;
    }

    try {
      chrome.storage.local.get(['solveCount', 'failCount', 'enabled']).then((result) => {
        if (state.shutdownRequested || !state.extensionValid) return;

        state.solveCount = result.solveCount || 0;
        state.failCount = result.failCount || 0;
        state.enabled = result.enabled !== undefined ? result.enabled : true;

        // Update status to indicate the solver is active/inactive
        log(state.enabled ? "Aktif" : "Nonaktif", false);

        // Start the main solver
        startSolver();
      }).catch(() => {
        // Fallback for environments without Promise support
        if (state.shutdownRequested || !state.extensionValid) return;

        try {
          chrome.storage.local.get(['solveCount', 'failCount', 'enabled'], (result) => {
            if (chrome.runtime.lastError) {
              console.warn('Failed to load settings, using defaults');
              state.enabled = true;
            } else {
              state.solveCount = result.solveCount || 0;
              state.failCount = result.failCount || 0;
              state.enabled = result.enabled !== undefined ? result.enabled : true;
            }

            if (!state.shutdownRequested && state.extensionValid) {
              log(state.enabled ? "Aktif" : "Nonaktif", false);
              startSolver();
            }
          });
        } catch (error) {
          console.warn('[reCAPTCHA Audio Solver] Extension context invalidated during fallback initialization');
          state.extensionValid = false;
          state.shutdownRequested = true;
        }
      });
    } catch (error) {
      console.warn('[reCAPTCHA Audio Solver] Extension context invalidated during initialization');
      state.extensionValid = false;
      state.shutdownRequested = true;
    }
  }

  // Enhanced message handling with better error handling and validation
  chrome.runtime.onMessage.addListener((request, _sender, sendResponse) => {
    if (!request || typeof request !== 'object') {
      sendResponse({ success: false, error: 'Invalid request' });
      return false;
    }

    try {
      switch (request.action) {
        case "toggleSolver":
          if (typeof request.enabled !== 'boolean') {
            sendResponse({ success: false, error: 'Invalid enabled value' });
            return false;
          }

          state.enabled = request.enabled;
          chrome.storage.local.set({ enabled: state.enabled }).then(() => {
            sendResponse({ success: true, solverRunning: state.enabled });
          }).catch(() => {
            // Fallback
            chrome.storage.local.set({ enabled: state.enabled });
            sendResponse({ success: true, solverRunning: state.enabled });
          });
          return true;

        case "getStats":
          sendResponse({
            solved: state.solveCount,
            failed: state.failCount,
            enabled: state.enabled,
            status: state.waiting ? 'processing' : (state.solved ? 'success' : 'waiting'),
            captchaDetected: !!$(CONFIG.SELECTORS.CHECKBOX) || !!$(CONFIG.SELECTORS.AUDIO_BUTTON)
          });
          return true;

        case "resetBotDetection":
          // Reset the solved state so the solver can try again
          state.solved = false;
          resetCounters();

          // Delete cookies for recaptcha.net domain
          log("Menghapus cookies untuk domain recaptcha.net...", false);

          if (state.extensionValid && !state.shutdownRequested) {
            safeSendMessage({
              action: "deleteCookies"
            }, (response) => {
              if (response && response.success) {
                log(`Berhasil menghapus ${response.count} cookies`, false);
              } else {
                log("Gagal menghapus cookies", true);
              }

              // Update status
              log(state.enabled ? "Aktif" : "Nonaktif", false);
            });
          }

          sendResponse({ success: true });
          return true;

        default:
          sendResponse({ success: false, error: 'Unknown action' });
          return false;
      }
    } catch (error) {
      console.error('Error handling message:', error);
      sendResponse({ success: false, error: error.message });
      return false;
    }
  });

  // Periodic health check to detect extension context invalidation
  function startHealthCheck() {
    // Check extension context every 5 seconds
    setInterval(() => {
      if (!state.shutdownRequested && state.extensionValid) {
        if (!isExtensionValid()) {
          console.warn('[reCAPTCHA Audio Solver] Extension context invalidated detected by health check');
          state.extensionValid = false;
          state.shutdownRequested = true;
          cleanup();
        }
      }
    }, 5000);
  }

  // Enhanced main solver function with better performance and error handling
  function startSolver() {
    // Clear any existing interval to prevent duplicates
    if (state.intervalId) {
      clearInterval(state.intervalId);
    }

    // Start health check
    startHealthCheck();

    state.intervalId = setInterval(() => {
      try {
        // Early returns for conditions that prevent processing
        if (!state.enabled || state.solved || state.processingCookies ||
            !state.extensionValid || state.shutdownRequested) {
          return;
        }

        // Check extension context validity periodically
        if (!isExtensionValid()) {
          console.warn('[reCAPTCHA Audio Solver] Extension context invalidated, stopping solver');
          state.extensionValid = false;
          state.shutdownRequested = true;
          cleanup();
          return;
        }

        // Throttle processing to prevent excessive DOM queries
        const now = Date.now();
        if (now - state.lastProcessTime < 200) return;
        state.lastProcessTime = now;

        try {
          // Check for bot detection
          const dos = $(CONFIG.SELECTORS.DOSCAPTCHA);
          if (dos && dos.innerText) {
            handleBotDetection();
            return;
          }

          // Process captcha elements
          processCaptchaElements();

        } catch (innerError) {
          // Handle extension context errors
          if (handleExtensionContextError(innerError, 'solver loop inner')) {
            return;
          }

          console.error('Error in solver loop inner:', innerError);
          // Continue processing despite other errors
        }

      } catch (outerError) {
        // Handle extension context errors at the outer level
        if (handleExtensionContextError(outerError, 'solver loop outer')) {
          return;
        }

        console.error('Error in solver loop outer:', outerError);
        // Continue processing despite other errors
      }
    }, CONFIG.POLLING_INTERVAL);
  }

  // Handle bot detection with proper state management
  function handleBotDetection() {
    const botMessage = "Bot terdeteksi! Coba lagi nanti.";
    log(botMessage, true);

    state.solved = true;
    state.failCount++;

    // Update storage with new stats and bot detection status
    const botData = {
      failCount: state.failCount,
      botDetected: true,
      lastStatus: botMessage
    };

    chrome.storage.local.set(botData).catch(() => {
      // Fallback for environments without Promise support
      chrome.storage.local.set(botData);
    });

    // Delete cookies for recaptcha.net domain
    if (state.extensionValid && !state.shutdownRequested) {
      safeSendMessage({
        action: "deleteCookies"
      });
    }

    // Send status update to popup
    updateStatus(botMessage);
  }

  // Process captcha elements with improved logic
  function processCaptchaElements() {
    // Click checkbox if not checked and not solved
    const checkbox = $(CONFIG.SELECTORS.CHECKBOX);
    if (checkbox && !isHidden(checkbox)) {
      handleCheckboxClick(checkbox);
      return;
    }

    // Click audio button if available
    const audioBtn = $(CONFIG.SELECTORS.AUDIO_BUTTON);
    const imgSel = $(CONFIG.SELECTORS.IMAGE_SELECT);
    if (audioBtn && !isHidden(audioBtn) && imgSel && !isHidden(imgSel)) {
      handleAudioButtonClick(audioBtn);
      return;
    }

    // Process audio if available
    processAudioChallenge();
  }

  // Handle checkbox click with cookie deletion
  function handleCheckboxClick(checkbox) {
    // Reset state for new captcha
    state.solved = false;
    state.audioUrl = "";
    resetCounters();

    // Set processing flag to prevent other operations
    state.processingCookies = true;

    // Delete cookies for recaptcha.net domain before clicking checkbox
    log("Menghapus cookies untuk domain recaptcha.net sebelum mengklik checkbox...", false);

    if (!state.extensionValid || state.shutdownRequested) {
      state.processingCookies = false;
      return;
    }

    const messageSent = safeSendMessage({
      action: "deleteCookies"
    }, (response) => {
      // Log result based on response
      if (response && response.success) {
        log(`Berhasil menghapus ${response.count} cookies`, false);
      } else {
        log("Gagal menghapus cookies", true);
      }

      // Click checkbox after cookies are deleted
      log("Mengklik checkbox setelah menghapus cookies", false);

      // Use a small delay to ensure the page is ready
      setTimeout(() => {
        if (checkbox && !isHidden(checkbox) && state.extensionValid && !state.shutdownRequested) {
          checkbox.click();
        }
        state.processingCookies = false;
      }, 100);
    });

    // If message sending failed, reset processing state
    if (!messageSent) {
      state.processingCookies = false;
    }
  }

  // Handle audio button click
  function handleAudioButtonClick(audioBtn) {
    // Reset attempt counters when switching to audio
    resetCounters();
    audioBtn.click();
    log("Tombol audio diklik.", false);
  }

  // Process audio challenge with enhanced logic
  function processAudioChallenge() {
    // Get all elements needed for audio processing at once
    const src = $(CONFIG.SELECTORS.AUDIO_SOURCE);
    const reload = $(CONFIG.SELECTORS.RELOAD_BUTTON);
    const errMsg = $(CONFIG.SELECTORS.AUDIO_ERROR_MESSAGE);
    const respField = $(CONFIG.SELECTORS.RESPONSE_FIELD);
    const resp = $(CONFIG.SELECTORS.AUDIO_RESPONSE);

    // Only process if we have an audio source
    if (!src || !src.src) return;

    // Handle audio errors and reload
    const needsReload = (!state.waiting && state.audioUrl === src.src && reload) ||
                       (errMsg && errMsg.innerText && reload && !reload.disabled);

    if (needsReload && reload && !reload.disabled) {
      reload.click();
      log("Reload audio.", false);
      state.audioUrl = ""; // reset audioUrl on reload
      return;
    }

    // Process new audio - enhanced condition check
    const shouldProcessAudio = !state.waiting && !state.solved &&
                              respField && !isHidden(respField) &&
                              (!resp || !resp.value) &&
                              state.audioUrl !== src.src;

    if (shouldProcessAudio) {
      state.audioUrl = src.src;
      log(`Audio baru: ${state.audioUrl}`, false);
      getTextFromAudio(state.audioUrl);
    }
  }

  // Enhanced cleanup function with comprehensive resource management
  function cleanup() {
    const cleanupStart = performance.now();
    console.log('[reCAPTCHA Audio Solver] Cleaning up resources...');

    try {
      // Set shutdown flags
      state.shutdownRequested = true;
      state.waiting = false;
      state.processing = false;
      state.processingCookies = false;
      state.extensionValid = false;

      // Clear all intervals and timeouts
      if (state.intervalId) {
        clearInterval(state.intervalId);
        state.intervalId = null;
      }

      if (state.timeoutId) {
        clearTimeout(state.timeoutId);
        state.timeoutId = null;
      }

      if (state.cleanupTimeoutId) {
        clearTimeout(state.cleanupTimeoutId);
        state.cleanupTimeoutId = null;
      }

      // Clear DOM cache and performance monitor
      domCache.clear();
      performanceMonitor.reset();

      // Cancel any pending debounced/throttled functions
      if (updateStatus.cancel) {
        updateStatus.cancel();
      }

      // Clear any pending operations
      state.audioUrl = "";
      state.lastAudioUrl = "";
      resetCounters();

      // Remove event listeners to prevent memory leaks
      try {
        window.removeEventListener('beforeunload', cleanup);
        window.removeEventListener('unload', cleanup);
        window.removeEventListener('error', handleGlobalError);
        window.removeEventListener('unhandledrejection', handleUnhandledRejection);
      } catch (error) {
        // Silently ignore errors during event listener removal
      }

      // Final storage update
      try {
        if (chrome?.storage?.local) {
          chrome.storage.local.set({
            lastCleanupTime: Date.now(),
            sessionUptime: Date.now() - state.startTime,
            cleanupReason: 'normal'
          }).catch(() => {
            // Silently handle storage errors during cleanup
          });
        }
      } catch (error) {
        // Ignore storage errors during cleanup
      }

      const cleanupDuration = performance.now() - cleanupStart;
      console.log(`[reCAPTCHA Audio Solver] Cleanup completed in ${cleanupDuration.toFixed(2)}ms`);

    } catch (error) {
      console.error('[reCAPTCHA Audio Solver] Error during cleanup:', error);
    }
  }

  // Enhanced global error handlers
  const handleGlobalError = (event) => {
    if (event.error) {
      handleExtensionContextError(event.error, 'global error handler');
    }
  };

  const handleUnhandledRejection = (event) => {
    if (event.reason) {
      handleExtensionContextError(event.reason, 'unhandled promise rejection');
    }
  };

  // Set up event listeners for cleanup and error handling
  window.addEventListener('beforeunload', cleanup, { passive: true });
  window.addEventListener('unload', cleanup, { passive: true });
  window.addEventListener('error', handleGlobalError, { passive: true });
  window.addEventListener('unhandledrejection', handleUnhandledRejection, { passive: true });

  // Enhanced initialization with better timing and validation
  function initializeExtension() {
    const initStart = performance.now();

    if (!isExtensionValid()) {
      console.warn('[reCAPTCHA Audio Solver] Extension context invalid at startup');
      return;
    }

    // Check if we're on a valid page
    if (!document.location.href.includes('recaptcha') &&
        !document.querySelector('iframe[src*="recaptcha"]')) {
      console.debug('[reCAPTCHA Audio Solver] Not a reCAPTCHA page, skipping initialization');
      return;
    }

    try {
      // Mark initialization as starting
      state.initializationComplete = false;

      // Initialize the solver
      initializeSolver();

      // Mark initialization as complete
      state.initializationComplete = true;

      const initDuration = performance.now() - initStart;
      log(`Extension initialized in ${initDuration.toFixed(2)}ms`, 'debug');

    } catch (error) {
      console.error('[reCAPTCHA Audio Solver] Initialization failed:', error);
      state.extensionValid = false;
      state.shutdownRequested = true;
    }
  }

  // Initialize with proper timing
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeExtension, { once: true });
  } else {
    // DOM is already ready
    setTimeout(initializeExtension, 100);
  }

  } catch (globalError) {
    console.error('[reCAPTCHA Audio Solver] Global error during initialization:', globalError);
    // Try to handle extension context errors even during initialization
    if (globalError.message && globalError.message.includes('Extension context invalidated')) {
      console.warn('[reCAPTCHA Audio Solver] Extension context invalidated during initialization');
    }
  }
})();
