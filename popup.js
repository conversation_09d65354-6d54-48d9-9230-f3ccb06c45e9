'use strict';

/**
 * reCAPTCHA Audio Solver v3.6.1 - Enhanced
 * Popup script with improved performance and user experience
 */

/**
 * Enhanced popup configuration with better organization and performance settings
 */
const POPUP_CONFIG = Object.freeze({
  VERSION: 'v3.6.1',

  // Animation settings
  ANIMATION: Object.freeze({
    DURATION: 250, // Reduced for better performance
    EASING: 'cubic-bezier(0.4, 0, 0.2, 1)', // Material design easing
    MAX_VALUE: 999,
    COUNTER_DURATION: 400
  }),

  // Performance settings
  PERFORMANCE: Object.freeze({
    DEBOUNCE_DELAY: 150,
    THROTTLE_DELAY: 100,
    UPDATE_INTERVAL: 1000,
    CACHE_TTL: 5000,
    MAX_RETRIES: 3
  }),

  // UI settings
  UI: Object.freeze({
    LOADING_TIMEOUT: 5000,
    ERROR_DISPLAY_TIME: 3000,
    SUCCESS_DISPLAY_TIME: 2000,
    TRANSITION_DELAY: 50
  }),

  // Accessibility settings
  A11Y: Object.freeze({
    FOCUS_VISIBLE_DELAY: 100,
    SCREEN_READER_DELAY: 200,
    HIGH_CONTRAST_THRESHOLD: 3
  })
});

// Enhanced element cache with validation and cleanup
class ElementCache {
  constructor() {
    this.cache = new Map();
    this.observers = new Map();
    this.lastCleanup = Date.now();
  }

  get(id) {
    const cached = this.cache.get(id);
    if (cached && cached.element && document.contains(cached.element)) {
      return cached.element;
    }

    // Element not in cache or removed from DOM, query again
    const element = document.getElementById(id);
    if (element) {
      this.cache.set(id, { element, timestamp: Date.now() });
    }
    return element;
  }

  set(id, element) {
    if (element && document.contains(element)) {
      this.cache.set(id, { element, timestamp: Date.now() });
    }
  }

  clear() {
    this.cache.clear();
    this.lastCleanup = Date.now();
  }

  cleanup() {
    const now = Date.now();
    for (const [id, cached] of this.cache) {
      if (now - cached.timestamp > POPUP_CONFIG.PERFORMANCE.CACHE_TTL ||
          !document.contains(cached.element)) {
        this.cache.delete(id);
      }
    }
    this.lastCleanup = now;
  }

  size() {
    return this.cache.size;
  }
}

// Global state management
const popupState = Object.seal({
  initialized: false,
  loading: false,
  error: null,
  lastUpdate: 0,
  retryCount: 0,
  animationsEnabled: true,
  highContrastMode: false,
  reducedMotion: false
});

const elementCache = new ElementCache();

// Global elements object to store cached DOM elements
const elements = {};

// Global initialization flag
let isInitialized = false;

// Enhanced time formatting with better performance
function formatLastUpdated(timestamp) {
  if (!timestamp) return t('noData');

  const now = Date.now();
  const diff = now - timestamp;

  // Optimized time calculations with constants
  const SECOND = 1000;
  const MINUTE = 60 * SECOND;
  const HOUR = 60 * MINUTE;

  if (diff < SECOND) return t('justNow');
  if (diff < MINUTE) return `${Math.floor(diff / SECOND)} ${t('secondsAgo')}`;
  if (diff < HOUR) return `${Math.floor(diff / MINUTE)} ${t('minutesAgo')}`;

  return new Date(timestamp).toLocaleTimeString();
}

/**
 * Enhanced value animation with better performance, accessibility, and memory management
 * @param {HTMLElement} element - The element to animate
 * @param {number} start - Starting value
 * @param {number} end - Ending value
 * @param {number} duration - Animation duration in milliseconds
 * @param {Object} options - Animation options
 */
function animateValue(element, start, end, duration = POPUP_CONFIG.ANIMATION.COUNTER_DURATION, options = {}) {
  // Enhanced input validation
  if (!element || !element.isConnected) {
    console.warn('Invalid element for animation');
    return;
  }

  if (typeof start !== 'number' || typeof end !== 'number') {
    element.textContent = end || 0;
    return;
  }

  // Check for reduced motion preference
  if (popupState.reducedMotion || !popupState.animationsEnabled) {
    element.textContent = end;
    return;
  }

  // Skip animation for very small changes
  const diff = Math.abs(end - start);
  if (diff < 2) {
    element.textContent = end;
    return;
  }

  // Enhanced value clamping with validation
  const clampedEnd = Math.min(Math.max(0, end), POPUP_CONFIG.ANIMATION.MAX_VALUE);
  const clampedStart = Math.min(Math.max(0, start), POPUP_CONFIG.ANIMATION.MAX_VALUE);

  // Intelligent duration adjustment
  const baseDuration = duration || POPUP_CONFIG.ANIMATION.COUNTER_DURATION;
  const adjustedDuration = Math.min(baseDuration, Math.max(200, diff * 30));

  // Cancel any existing animation
  if (element._animationController) {
    element._animationController.abort();
  }

  // Create abort controller for cleanup
  const controller = new AbortController();
  element._animationController = controller;

  const startTime = performance.now();
  let lastValue = clampedStart;

  const animate = (currentTime) => {
    // Check if animation was cancelled
    if (controller.signal.aborted) {
      return;
    }

    const elapsedTime = currentTime - startTime;
    const progress = Math.min(elapsedTime / adjustedDuration, 1);

    // Enhanced easing function
    const easedProgress = options.easing === 'linear' ? progress :
      1 - Math.pow(1 - progress, 3); // easeOutCubic

    const currentValue = Math.round(easedProgress * (clampedEnd - clampedStart) + clampedStart);

    // Performance optimization: only update DOM if value changed
    if (currentValue !== lastValue && element.isConnected) {
      element.textContent = currentValue;
      lastValue = currentValue;

      // Accessibility: announce significant changes
      if (options.announceChanges && diff > 10 && currentValue % 10 === 0) {
        element.setAttribute('aria-live', 'polite');
      }
    }

    if (progress < 1) {
      requestAnimationFrame(animate);
    } else {
      // Ensure final value is exact
      if (element.isConnected) {
        element.textContent = clampedEnd;
      }

      // Cleanup
      element._animationController = null;

      // Call completion callback if provided
      if (options.onComplete && typeof options.onComplete === 'function') {
        options.onComplete();
      }
    }
  };

  requestAnimationFrame(animate);
}

/**
 * Enhanced status animation with better performance, accessibility, and error handling
 * @param {HTMLElement} element - The element to update
 * @param {string} text - The new text content
 * @param {Object} options - Animation options
 */
function updateStatusWithAnimation(element, text, options = {}) {
  // Enhanced input validation
  if (!element || !element.isConnected || typeof text !== 'string') {
    console.warn('Invalid parameters for status animation');
    return;
  }

  // Skip animation if text hasn't changed
  if (element.textContent === text) return;

  // Check for reduced motion
  if (popupState.reducedMotion) {
    element.textContent = text;
    return;
  }

  // Cancel any existing animation
  if (element._statusController) {
    element._statusController.abort();
  }

  // Create new abort controller
  const controller = new AbortController();
  element._statusController = controller;

  try {
    // Enhanced transition with better performance
    const transitionDuration = options.duration || POPUP_CONFIG.PERFORMANCE.DEBOUNCE_DELAY;
    element.style.transition = `opacity ${transitionDuration}ms ${POPUP_CONFIG.ANIMATION.EASING}`;
    element.style.opacity = '0';

    // Use timeout with abort signal
    const timeoutId = setTimeout(() => {
      if (controller.signal.aborted || !element.isConnected) return;

      // Update text content
      element.textContent = text;

      // Update accessibility attributes
      if (options.announceChange) {
        element.setAttribute('aria-live', 'polite');
        element.setAttribute('aria-atomic', 'true');
      }

      // Smooth fade-in with requestAnimationFrame
      requestAnimationFrame(() => {
        if (controller.signal.aborted || !element.isConnected) return;

        element.style.opacity = '1';

        // Cleanup after animation
        setTimeout(() => {
          if (element.isConnected) {
            element.style.transition = '';
            element._statusController = null;
          }
        }, transitionDuration);
      });
    }, transitionDuration / 2);

    // Handle abort
    controller.signal.addEventListener('abort', () => {
      clearTimeout(timeoutId);
      if (element.isConnected) {
        element.style.transition = '';
        element.style.opacity = '';
      }
    });

  } catch (error) {
    console.error('Error in status animation:', error);
    // Fallback: direct text update
    element.textContent = text;
    element._statusController = null;
  }
}

// Enhanced UI language update with better performance and caching
function updateUILanguage() {
  // Cache elements if not already cached
  if (!elements.title) {
    elements.title = document.getElementById('title');
    elements.statusLabel = document.getElementById('statusLabel');
    elements.successLabel = document.getElementById('successLabel');
    elements.failLabel = document.getElementById('failLabel');
    elements.toggleStatus = document.getElementById('toggleStatus');
    elements.status = document.getElementById('status');
    elements.solverToggle = document.getElementById('solverToggle');
  }

  // Define element-to-translation mapping
  const translations = [
    [elements.title, 'title'],
    [elements.statusLabel, 'statusLabel'],
    [elements.successLabel, 'successLabel'],
    [elements.failLabel, 'failLabel']
  ];

  // Update all text elements efficiently
  translations.forEach(([element, key]) => {
    if (element) element.textContent = t(key);
  });

  // Handle toggle status with state dependency
  if (elements.toggleStatus && elements.solverToggle) {
    elements.toggleStatus.textContent = elements.solverToggle.checked ?
      t('toggleActive') : t('toggleInactive');
  }

  // Update status text with pattern matching
  if (elements.status) {
    updateStatusTranslation(elements.status);
  }
}

// Separate function for status translation with optimized pattern matching
function updateStatusTranslation(statusElement) {
  const currentStatus = statusElement.textContent;
  if (!currentStatus) return;

  // Optimized status mapping with regex for better performance
  const statusPatterns = [
    { regex: /^(Aktif|Active|Activo)$/i, key: 'active' },
    { regex: /^(Nonaktif|Inactive|Inactivo)$/i, key: 'inactive' },
    { regex: /^(Memproses|Processing|Procesando)/i, key: 'processing' },
    { regex: /^(Memuat|Loading|Cargando)/i, key: 'loading' },
    { regex: /(Bot terdeteksi|Bot detected|Bot detectado)/i, key: 'botDetected' }
  ];

  // Find matching pattern and update
  for (const { regex, key } of statusPatterns) {
    if (regex.test(currentStatus)) {
      statusElement.textContent = t(key);
      return;
    }
  }
}

// Enhanced dark mode toggle with better performance and error handling
function toggleDarkMode() {
  // Cache icon paths as constants for better performance
  const ICON_PATHS = Object.freeze({
    light: '<path d="M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2M12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4M12,6A6,6 0 0,1 18,12A6,6 0 0,1 12,18A6,6 0 0,1 6,12A6,6 0 0,1 12,6M12,8A4,4 0 0,0 8,12A4,4 0 0,0 12,16A4,4 0 0,0 16,12A4,4 0 0,0 12,8Z" />',
    dark: '<path d="M12,18C11.11,18 10.26,17.8 9.5,17.45C11.56,16.5 13,14.42 13,12C13,9.58 11.56,7.5 9.5,6.55C10.26,6.2 11.11,6 12,6A6,6 0 0,1 18,12A6,6 0 0,1 12,18M20,8.69V4H15.31L12,0.69L8.69,4H4V8.69L0.69,12L4,15.31V20H8.69L12,23.31L15.31,20H20V15.31L23.31,12L20,8.69Z" />'
  });

  try {
    const isDarkMode = document.body.getAttribute('data-theme') === 'dark';
    const newMode = isDarkMode ? 'light' : 'dark';

    // Update theme attribute efficiently
    if (isDarkMode) {
      document.body.removeAttribute('data-theme');
    } else {
      document.body.setAttribute('data-theme', 'dark');
    }

    // Save preference with error handling
    try {
      localStorage.setItem('reCAPTCHA_solver_theme', newMode);
    } catch (storageError) {
      console.warn('Failed to save theme preference:', storageError.message);
      // Continue with theme change even if storage fails
    }

    // Update icon efficiently
    if (!elements.themeIcon) {
      elements.themeIcon = document.getElementById('themeIcon');
    }

    if (elements.themeIcon) {
      elements.themeIcon.innerHTML = ICON_PATHS[newMode];
    }

    // Dispatch custom event for other components that might need to know about theme change
    document.dispatchEvent(new CustomEvent('themeChanged', {
      detail: { theme: newMode }
    }));

  } catch (error) {
    console.error('Error toggling dark mode:', error);
  }
}

/**
 * Enhanced DOM initialization with better error handling, validation, and accessibility
 * @returns {boolean} - Whether initialization was successful
 */
function initializeElements() {
  try {
    // Define element mappings with validation
    const elementMappings = [
      { key: 'solverToggle', id: 'solverToggle', critical: true },
      { key: 'toggleStatus', id: 'toggleStatus', critical: true },
      { key: 'statusElement', id: 'status', critical: true },
      { key: 'solveCountElement', id: 'solveCount', critical: true },
      { key: 'failCountElement', id: 'failCount', critical: true },
      { key: 'langEN', id: 'langEN', critical: false },
      { key: 'langID', id: 'langID', critical: false },
      { key: 'langES', id: 'langES', critical: false },
      { key: 'themeToggle', id: 'themeToggle', critical: false },
      { key: 'retryButton', id: 'retryButton', critical: false },
      { key: 'retryText', selector: '#retryButton .retry-text', critical: false },
      { key: 'statusContainer', selector: '.status-container', critical: false },
      { key: 'title', id: 'title', critical: false },
      { key: 'statusLabel', id: 'statusLabel', critical: false },
      { key: 'successLabel', id: 'successLabel', critical: false },
      { key: 'failLabel', id: 'failLabel', critical: false }
    ];

    const missingCritical = [];
    const missingOptional = [];

    // Cache elements with enhanced validation
    elementMappings.forEach(({ key, id, selector, critical }) => {
      let element = null;

      try {
        if (id) {
          element = document.getElementById(id);
        } else if (selector) {
          element = document.querySelector(selector);
        }

        if (element) {
          elementCache.set(key, element);
          elements[key] = element; // Also store in global elements object

          // Add accessibility enhancements
          if (critical && !element.getAttribute('aria-label') && !element.getAttribute('aria-labelledby')) {
            console.warn(`Critical element ${key} missing accessibility label`);
          }
        } else {
          if (critical) {
            missingCritical.push(key);
          } else {
            missingOptional.push(key);
          }
        }
      } catch (error) {
        console.error(`Error caching element ${key}:`, error);
        if (critical) {
          missingCritical.push(key);
        }
      }
    });

    // Log missing elements
    if (missingOptional.length > 0) {
      console.warn('Missing optional elements:', missingOptional);
    }

    if (missingCritical.length > 0) {
      console.error('Missing critical elements:', missingCritical);
      return false;
    }

    // Validate element functionality
    const solverToggle = elementCache.get('solverToggle');
    if (solverToggle && typeof solverToggle.checked !== 'boolean') {
      console.error('Solver toggle is not a valid checkbox');
      return false;
    }

    // Set up accessibility features
    setupAccessibilityFeatures();

    console.log(`Successfully initialized ${elementMappings.length - missingOptional.length}/${elementMappings.length} elements`);
    return true;

  } catch (error) {
    console.error('Failed to initialize elements:', error);
    return false;
  }
}

/**
 * Set up accessibility features for better user experience
 */
function setupAccessibilityFeatures() {
  try {
    // Check for reduced motion preference
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    popupState.reducedMotion = mediaQuery.matches;

    // Listen for changes
    mediaQuery.addEventListener('change', (e) => {
      popupState.reducedMotion = e.matches;
      popupState.animationsEnabled = !e.matches;
    });

    // Check for high contrast preference
    const highContrastQuery = window.matchMedia('(prefers-contrast: high)');
    popupState.highContrastMode = highContrastQuery.matches;

    highContrastQuery.addEventListener('change', (e) => {
      popupState.highContrastMode = e.matches;
      document.body.classList.toggle('high-contrast', e.matches);
    });

    // Set initial high contrast class
    if (popupState.highContrastMode) {
      document.body.classList.add('high-contrast');
    }

    // Add focus management
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Tab') {
        document.body.classList.add('keyboard-navigation');
      }
    });

    document.addEventListener('mousedown', () => {
      document.body.classList.remove('keyboard-navigation');
    });

  } catch (error) {
    console.error('Error setting up accessibility features:', error);
  }
}

// Enhanced theme initialization with error handling
function initializeTheme() {
  try {
    const savedTheme = localStorage.getItem('reCAPTCHA_solver_theme');
    if (savedTheme === 'dark') {
      document.body.setAttribute('data-theme', 'dark');
      const themeIcon = document.getElementById('themeIcon');
      if (themeIcon) {
        themeIcon.innerHTML = '<path d="M12,18C11.11,18 10.26,17.8 9.5,17.45C11.56,16.5 13,14.42 13,12C13,9.58 11.56,7.5 9.5,6.55C10.26,6.2 11.11,6 12,6A6,6 0 0,1 18,12A6,6 0 0,1 12,18M20,8.69V4H15.31L12,0.69L8.69,4H4V8.69L0.69,12L4,15.31V20H8.69L12,23.31L15.31,20H20V15.31L23.31,12L20,8.69Z" />';
      }
    }
  } catch (error) {
    console.warn('Failed to initialize theme:', error.message);
  }
}

// Enhanced language initialization
function initializeLanguage() {
  try {
    const currentLang = getLanguage();

    // Set language button states efficiently
    const langButtons = [
      [elements.langEN, 'en'],
      [elements.langID, 'id'],
      [elements.langES, 'es']
    ];

    langButtons.forEach(([button, lang]) => {
      if (button) {
        button.classList.toggle('lang-active', currentLang === lang);
      }
    });

    // Update UI with current language
    updateUILanguage();

    // Set retry button text
    if (elements.retryText) {
      elements.retryText.textContent = t('tryAgain');
    }
  } catch (error) {
    console.error('Failed to initialize language:', error);
  }
}

/**
 * Enhanced main initialization function with better error handling and performance
 */
async function initializePopup() {
  const initStart = performance.now();

  try {
    // Set loading state
    popupState.loading = true;
    popupState.error = null;

    // Initialize elements with validation
    if (!initializeElements()) {
      throw new Error('Failed to initialize critical elements');
    }

    // Mark as initialized
    popupState.initialized = true;
    isInitialized = true;

    // Add loading state to UI
    const statusContainer = elementCache.get('statusContainer');
    if (statusContainer) {
      statusContainer.classList.add('loading');
    }

    // Initialize theme system
    await initializeTheme();

    // Initialize language system
    initializeLanguage();

    // Show initial loading status
    const statusElement = elementCache.get('statusElement');
    if (statusElement) {
      updateStatusWithAnimation(statusElement, t('loading'), { announceChange: true });
    }

    // Set up event listeners
    setupEventListeners();

    // Load initial data
    await loadInitialData();

    // Clear loading state
    popupState.loading = false;
    if (statusContainer) {
      statusContainer.classList.remove('loading');
    }

    const initDuration = performance.now() - initStart;
    console.log(`Popup initialized successfully in ${initDuration.toFixed(2)}ms`);

  } catch (error) {
    console.error('Failed to initialize popup:', error);

    // Set error state
    popupState.loading = false;
    popupState.error = error.message;

    // Show error to user
    showError('Initialization failed. Please refresh the popup.');
  }
}

/**
 * Show error message to user with retry option
 * @param {string} message - Error message to display
 */
function showError(message) {
  try {
    const statusElement = elementCache.get('statusElement');
    if (statusElement) {
      updateStatusWithAnimation(statusElement, message, {
        announceChange: true,
        duration: POPUP_CONFIG.UI.ERROR_DISPLAY_TIME
      });
    }

    // Add error styling
    const statusContainer = elementCache.get('statusContainer');
    if (statusContainer) {
      statusContainer.classList.add('error');

      // Remove error styling after timeout
      setTimeout(() => {
        statusContainer.classList.remove('error');
      }, POPUP_CONFIG.UI.ERROR_DISPLAY_TIME);
    }

  } catch (error) {
    console.error('Error showing error message:', error);
  }
}

/**
 * Enhanced error boundary for popup
 */
function setupErrorBoundary() {
  // Global error handler
  window.addEventListener('error', (event) => {
    console.error('Popup error:', event.error);

    if (!popupState.initialized) {
      showError('Failed to load popup. Please try again.');
    }
  });

  // Unhandled promise rejection handler
  window.addEventListener('unhandledrejection', (event) => {
    console.error('Unhandled promise rejection in popup:', event.reason);

    if (event.reason && event.reason.message) {
      showError('An error occurred. Please try again.');
    }
  });
}

// Initialize popup when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializePopup, { once: true });
} else {
  // DOM is already ready
  initializePopup();
}

// Set up error boundary
setupErrorBoundary();



// Enhanced event listeners setup with better error handling
function setupEventListeners() {
  // Theme toggle event listener
  if (elements.themeToggle) {
    elements.themeToggle.addEventListener('click', toggleDarkMode);
  }

  // Retry button event listener with enhanced functionality
  if (elements.retryButton) {
    elements.retryButton.addEventListener('click', handleRetryClick);
  }

  // Language switching event listeners
  setupLanguageListeners();

  // Solver toggle event listener
  if (elements.solverToggle) {
    elements.solverToggle.addEventListener('change', handleSolverToggle);
  }

  // Storage change listener
  chrome.storage.onChanged.addListener(handleStorageChange);
}

// Enhanced retry button handler
function handleRetryClick() {
  try {
    // Reset bot detection status
    chrome.storage.local.set({ botDetected: false }).catch(() => {
      // Fallback for environments without Promise support
      chrome.storage.local.set({ botDetected: false });
    });

    // Update UI
    if (elements.statusElement && elements.solverToggle) {
      updateStatusWithAnimation(
        elements.statusElement,
        elements.solverToggle.checked ? t('active') : t('inactive')
      );
    }

    // Update status container
    if (elements.statusContainer && elements.solverToggle) {
      elements.statusContainer.classList.remove('bot-detected');
      elements.statusContainer.style.borderLeftColor =
        elements.solverToggle.checked ? '#4285F4' : '#EA4335';
    }

    // Hide retry button
    if (elements.retryButton) {
      elements.retryButton.classList.remove('visible');
    }

    // Notify all tabs with reCAPTCHA to reset
    notifyTabsToReset();

  } catch (error) {
    console.error('Error handling retry click:', error);
  }
}

// Enhanced tab notification with better error handling
function notifyTabsToReset() {
  const urlPatterns = [
    "*://*/recaptcha/*",
    "*://*.google.com/recaptcha/*",
    "*://*.recaptcha.net/recaptcha/*"
  ];

  try {
    chrome.tabs.query({ url: urlPatterns }, (tabs) => {
      if (chrome.runtime.lastError) {
        console.warn('Failed to query tabs:', chrome.runtime.lastError.message);
        return;
      }

      tabs.forEach(tab => {
        try {
          chrome.tabs.sendMessage(tab.id, {
            action: "resetBotDetection"
          }, (response) => {
            // Handle response or errors silently
            if (chrome.runtime.lastError) {
              // Expected for tabs that don't have the content script
            }
          });
        } catch (error) {
          // Silently ignore individual tab errors
        }
      });
    });
  } catch (error) {
    console.error('Error notifying tabs:', error);
  }
}

// Enhanced language switching with better performance
function setupLanguageListeners() {
  const languageButtons = [
    { element: elements.langEN, lang: 'en', others: [elements.langID, elements.langES] },
    { element: elements.langID, lang: 'id', others: [elements.langEN, elements.langES] },
    { element: elements.langES, lang: 'es', others: [elements.langEN, elements.langID] }
  ];

  languageButtons.forEach(({ element, lang, others }) => {
    if (element) {
      element.addEventListener('click', () => handleLanguageSwitch(lang, element, others));
    }
  });
}

// Optimized language switch handler with debouncing
const handleLanguageSwitch = debounce((lang, button, otherButtons) => {
  try {
    if (getLanguage() !== lang) {
      setLanguage(lang);

      // Update active state for all buttons efficiently
      button.classList.add('lang-active');
      otherButtons.forEach(btn => {
        if (btn) btn.classList.remove('lang-active');
      });

      // Update UI with new language
      updateUILanguage();
    }
  } catch (error) {
    console.error('Error switching language:', error);
  }
}, POPUP_CONFIG.DEBOUNCE_DELAY);

// Debounce utility function for better performance
function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

// Enhanced data loading with better error handling and performance
function loadInitialData() {
  try {
    chrome.storage.local.get(['enabled', 'solveCount', 'failCount', 'botDetected', 'lastStatus'])
      .then(handleInitialData)
      .catch(() => {
        // Fallback for environments without Promise support
        chrome.storage.local.get(['enabled', 'solveCount', 'failCount', 'botDetected', 'lastStatus'], handleInitialData);
      });
  } catch (error) {
    console.error('Error loading initial data:', error);
    // Set default values
    handleInitialData({});
  }
}

// Handle initial data with enhanced UI updates
function handleInitialData(data) {
  try {
    // Set default values if not found
    const isEnabled = data.enabled !== undefined ? data.enabled : true;
    const solveCount = data.solveCount || 0;
    const failCount = data.failCount || 0;
    const botDetected = data.botDetected || false;

    // Update solver toggle and status
    if (elements.solverToggle && elements.toggleStatus) {
      elements.solverToggle.checked = isEnabled;
      elements.toggleStatus.textContent = isEnabled ? t('toggleActive') : t('toggleInactive');
    }

    // Update status based on bot detection
    if (botDetected || (data.lastStatus && data.lastStatus.includes('Bot terdeteksi'))) {
      handleBotDetectedState();
    } else {
      handleNormalState(isEnabled);
    }

    // Update counters with animation
    if (elements.solveCountElement && elements.failCountElement) {
      animateValue(elements.solveCountElement, 0, solveCount, 600);
      animateValue(elements.failCountElement, 0, failCount, 600);
    }

  } catch (error) {
    console.error('Error handling initial data:', error);
  }
}

// Handle bot detected state
function handleBotDetectedState() {
  if (elements.statusElement) {
    updateStatusWithAnimation(elements.statusElement, t('botDetected'));
  }

  if (elements.statusContainer) {
    elements.statusContainer.classList.remove('loading', 'active', 'inactive', 'processing');
    elements.statusContainer.classList.add('bot-detected');
    elements.statusContainer.style.borderLeftColor = '#FF0000';
  }

  if (elements.retryButton) {
    elements.retryButton.classList.add('visible');
  }

  if (elements.retryText) {
    elements.retryText.textContent = t('tryAgain');
  }
}

// Handle normal state
function handleNormalState(isEnabled) {
  if (elements.statusElement) {
    updateStatusWithAnimation(elements.statusElement, isEnabled ? t('active') : t('inactive'));
  }

  if (elements.statusContainer) {
    elements.statusContainer.classList.remove('loading', 'bot-detected', 'processing');

    if (isEnabled) {
      elements.statusContainer.classList.add('active');
      elements.statusContainer.classList.remove('inactive');
      elements.statusContainer.style.borderLeftColor = '#4285F4';
    } else {
      elements.statusContainer.classList.add('inactive');
      elements.statusContainer.classList.remove('active');
      elements.statusContainer.style.borderLeftColor = '#EA4335';
    }
  }

  if (elements.retryButton) {
    elements.retryButton.classList.remove('visible');
  }
}

// Enhanced solver toggle handler with better performance and error handling
function handleSolverToggle() {
  try {
    const enabled = elements.solverToggle.checked;

    // Update toggle status text
    if (elements.toggleStatus) {
      elements.toggleStatus.textContent = enabled ? t('toggleActive') : t('toggleInactive');
    }

    // Reset bot detection status when toggling
    chrome.storage.local.get(['botDetected'])
      .then(data => handleToggleWithBotCheck(enabled, data))
      .catch(() => {
        // Fallback for environments without Promise support
        chrome.storage.local.get(['botDetected'], data => handleToggleWithBotCheck(enabled, data));
      });

  } catch (error) {
    console.error('Error handling solver toggle:', error);
  }
}

// Handle toggle with bot detection check
function handleToggleWithBotCheck(enabled, data) {
  try {
    const wasBotDetected = data.botDetected || false;

    // If bot was detected, reset the status
    if (wasBotDetected) {
      chrome.storage.local.set({ botDetected: false }).catch(() => {
        chrome.storage.local.set({ botDetected: false });
      });
    }

    // Update status with animation
    if (elements.statusElement) {
      updateStatusWithAnimation(elements.statusElement, enabled ? t('active') : t('inactive'));
    }

    // Update status container
    updateStatusContainer(enabled);

    // Save state to storage
    chrome.storage.local.set({ enabled }).catch(() => {
      chrome.storage.local.set({ enabled });
    });

    // Notify tabs about the toggle
    notifyTabsAboutToggle(enabled);

  } catch (error) {
    console.error('Error in handleToggleWithBotCheck:', error);
  }
}

// Update status container efficiently
function updateStatusContainer(enabled) {
  if (!elements.statusContainer) return;

  try {
    elements.statusContainer.classList.remove('bot-detected', 'loading', 'processing');

    if (enabled) {
      elements.statusContainer.classList.add('active');
      elements.statusContainer.classList.remove('inactive');
      elements.statusContainer.style.borderLeftColor = '#4285F4';
    } else {
      elements.statusContainer.classList.add('inactive');
      elements.statusContainer.classList.remove('active');
      elements.statusContainer.style.borderLeftColor = '#EA4335';
    }
  } catch (error) {
    console.error('Error updating status container:', error);
  }
}

// Notify tabs about solver toggle
function notifyTabsAboutToggle(enabled) {
  const urlPatterns = [
    "*://*/recaptcha/*",
    "*://*.google.com/recaptcha/*",
    "*://*.recaptcha.net/recaptcha/*"
  ];

  try {
    chrome.tabs.query({ url: urlPatterns }, (tabs) => {
      if (chrome.runtime.lastError) {
        console.warn('Failed to query tabs for toggle:', chrome.runtime.lastError.message);
        return;
      }

      tabs.forEach(tab => {
        try {
          chrome.tabs.sendMessage(tab.id, {
            action: "toggleSolver",
            enabled
          }, () => {
            // Handle response or errors silently
            if (chrome.runtime.lastError) {
              // Expected for tabs that don't have the content script
            }
          });
        } catch (error) {
          // Silently ignore individual tab errors
        }
      });
    });
  } catch (error) {
    console.error('Error notifying tabs about toggle:', error);
  }
}

// Enhanced storage change handler with better performance and error handling
function handleStorageChange(changes, namespace) {
  if (namespace !== 'local' || !isInitialized) return;

  try {
    // Handle counter updates with throttling
    if (changes.solveCount && elements.solveCountElement) {
      const currentValue = parseInt(elements.solveCountElement.textContent) || 0;
      animateValue(elements.solveCountElement, currentValue, changes.solveCount.newValue, 400);
    }

    if (changes.failCount && elements.failCountElement) {
      const currentValue = parseInt(elements.failCountElement.textContent) || 0;
      animateValue(elements.failCountElement, currentValue, changes.failCount.newValue, 400);
    }

    // Handle bot detection status changes
    if (changes.botDetected) {
      if (changes.botDetected.newValue === true) {
        handleBotDetectedChange();
      } else if (changes.botDetected.newValue === false && elements.retryButton) {
        elements.retryButton.classList.remove('visible');
      }
    }

    // Handle lastStatus changes for bot detection
    if (changes.lastStatus && changes.lastStatus.newValue &&
        changes.lastStatus.newValue.includes('Bot terdeteksi')) {
      handleBotDetectedChange();
    }

    // Handle enabled state changes
    if (changes.enabled && elements.solverToggle &&
        elements.solverToggle.checked !== changes.enabled.newValue) {
      handleEnabledStateChange(changes.enabled.newValue);
    }

  } catch (error) {
    console.error('Error handling storage change:', error);
  }
}

// Handle bot detected change
function handleBotDetectedChange() {
  if (elements.statusElement) {
    updateStatusWithAnimation(elements.statusElement, t('botDetected'));
  }

  if (elements.statusContainer) {
    elements.statusContainer.classList.remove('loading');
    elements.statusContainer.classList.add('bot-detected');
    elements.statusContainer.style.borderLeftColor = '#FF0000';
  }

  if (elements.retryButton) {
    elements.retryButton.classList.add('visible');
  }

  if (elements.retryText) {
    elements.retryText.textContent = t('tryAgain');
  }
}

// Handle enabled state change
function handleEnabledStateChange(newEnabledValue) {
  try {
    if (elements.solverToggle && elements.toggleStatus) {
      elements.solverToggle.checked = newEnabledValue;
      elements.toggleStatus.textContent = newEnabledValue ? t('toggleActive') : t('toggleInactive');
    }

    // Only update status if not in bot detected state
    chrome.storage.local.get(['botDetected'])
      .then(data => {
        if (!data.botDetected) {
          updateStatusForEnabledChange(newEnabledValue);
        }
      })
      .catch(() => {
        // Fallback
        chrome.storage.local.get(['botDetected'], (data) => {
          if (!chrome.runtime.lastError && !data.botDetected) {
            updateStatusForEnabledChange(newEnabledValue);
          }
        });
      });

  } catch (error) {
    console.error('Error handling enabled state change:', error);
  }
}

// Update status for enabled change
function updateStatusForEnabledChange(enabled) {
  if (elements.statusElement) {
    updateStatusWithAnimation(elements.statusElement, enabled ? t('active') : t('inactive'));
  }

  if (elements.statusContainer) {
    elements.statusContainer.classList.remove('loading', 'bot-detected', 'processing');

    if (enabled) {
      elements.statusContainer.classList.add('active');
      elements.statusContainer.classList.remove('inactive');
      elements.statusContainer.style.borderLeftColor = '#4285F4';
    } else {
      elements.statusContainer.classList.add('inactive');
      elements.statusContainer.classList.remove('active');
      elements.statusContainer.style.borderLeftColor = '#EA4335';
    }
  }
}
